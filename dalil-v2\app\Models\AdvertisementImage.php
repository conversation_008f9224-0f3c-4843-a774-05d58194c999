<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdvertisementImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'advertisement_id',
        'image_path',
        'image_name',
        'sort_order',
        'is_primary',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
    ];

    // العلاقات
    public function advertisement()
    {
        return $this->belongsTo(Advertisement::class);
    }

    // دوال مساعدة
    public function getFullUrl()
    {
        return asset('storage/' . $this->image_path);
    }

    public function isPrimary()
    {
        return $this->is_primary;
    }
}
