<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\NotificationSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء المدير العام
        $superAdmin = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '967777777777',
            'user_type' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // ربط المدير العام بدور المدير العام
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->roles()->attach($superAdminRole);

        // إنشاء إعدادات الإشعارات للمدير العام
        NotificationSetting::createDefaultForUser($superAdmin->id);

        // إنشاء مدير إضافي
        $admin = User::create([
            'name' => 'مدير المحتوى',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '967777777778',
            'user_type' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // ربط المدير بدور المدير
        $adminRole = Role::where('name', 'admin')->first();
        $admin->roles()->attach($adminRole);

        // إنشاء إعدادات الإشعارات للمدير
        NotificationSetting::createDefaultForUser($admin->id);

        // إنشاء مشرف
        $moderator = User::create([
            'name' => 'مشرف الإعلانات',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '967777777779',
            'user_type' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // ربط المشرف بدور المشرف
        $moderatorRole = Role::where('name', 'moderator')->first();
        $moderator->roles()->attach($moderatorRole);

        // إنشاء إعدادات الإشعارات للمشرف
        NotificationSetting::createDefaultForUser($moderator->id);

        // إنشاء مستخدم تجريبي للمعارض
        $showroom = User::create([
            'name' => 'معرض الأمانة للسيارات',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '967777777780',
            'user_type' => 'showroom',
            'status' => 'active',
            'city' => 'صنعاء',
            'address' => 'شارع الزبيري',
            'bio' => 'معرض متخصص في بيع السيارات المستعملة والجديدة',
            'email_verified_at' => now(),
        ]);

        // إنشاء إعدادات الإشعارات للمعرض
        NotificationSetting::createDefaultForUser($showroom->id);

        // إنشاء مستخدم تجريبي فردي
        $individual = User::create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '967777777781',
            'user_type' => 'individual',
            'status' => 'active',
            'city' => 'عدن',
            'address' => 'كريتر',
            'email_verified_at' => now(),
        ]);

        // إنشاء إعدادات الإشعارات للمستخدم الفردي
        NotificationSetting::createDefaultForUser($individual->id);

        $this->command->info('تم إنشاء المستخدمين الإداريين والتجريبيين بنجاح');
        $this->command->info('بيانات الدخول:');
        $this->command->info('المدير العام: <EMAIL> / password123');
        $this->command->info('مدير المحتوى: <EMAIL> / password123');
        $this->command->info('المشرف: <EMAIL> / password123');
        $this->command->info('المعرض: <EMAIL> / password123');
        $this->command->info('المستخدم الفردي: <EMAIL> / password123');
    }
}
