/**
 * نظام الجداول التفاعلية مع AJAX
 * يمكن إعادة استخدامه لجميع الجداول في لوحة التحكم
 */

class AjaxTable {
    constructor(options) {
        this.options = {
            // الإعدادات الافتراضية
            tableId: '#data-table',
            filtersFormId: '#filters-form',
            loadingIndicatorId: '#loading-indicator',
            paginationContainerId: '#pagination-container',
            counterId: '#data-count',
            dataUrl: '',
            perPage: 15,
            sortBy: 'created_at',
            sortDir: 'desc',
            searchDelay: 500,
            ...options
        };

        this.currentPage = 1;
        this.sortBy = this.options.sortBy;
        this.sortDir = this.options.sortDir;

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadData();
    }

    bindEvents() {
        // البحث المباشر
        $(this.options.filtersFormId + ' input[name="search"]').on('input', 
            this.debounce(() => {
                this.currentPage = 1;
                this.loadData();
            }, this.options.searchDelay)
        );

        // تغيير الفلاتر
        $(this.options.filtersFormId + ' select, ' + this.options.filtersFormId + ' input[type="checkbox"]')
            .on('change', () => {
                this.currentPage = 1;
                this.loadData();
            });

        // مسح الفلاتر
        $('#clear-filters').on('click', () => {
            $(this.options.filtersFormId)[0].reset();
            this.currentPage = 1;
            this.loadData();
        });

        // الترتيب
        $('.sortable').on('click', (e) => {
            const $element = $(e.currentTarget);
            const newSortBy = $element.data('sort');
            
            if (this.sortBy === newSortBy) {
                this.sortDir = this.sortDir === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortBy = newSortBy;
                this.sortDir = 'asc';
            }
            
            // تحديث أيقونات الترتيب
            $('.sortable i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
            $element.find('i').removeClass('fa-sort')
                .addClass(this.sortDir === 'asc' ? 'fa-sort-up' : 'fa-sort-down');
            
            this.currentPage = 1;
            this.loadData();
        });

        // التعامل مع الترقيم
        $(document).on('click', this.options.paginationContainerId + ' .pagination a', (e) => {
            e.preventDefault();
            const url = $(e.target).closest('a').attr('href');
            if (url) {
                const page = new URL(url).searchParams.get('page');
                if (page) {
                    this.currentPage = parseInt(page);
                    this.loadData();
                }
            }
        });
    }

    loadData() {
        $(this.options.loadingIndicatorId).removeClass('d-none');

        const formData = $(this.options.filtersFormId).serialize();
        const params = new URLSearchParams(formData);
        params.append('page', this.currentPage);
        params.append('sort_by', this.sortBy);
        params.append('sort_dir', this.sortDir);

        $.ajax({
            url: this.options.dataUrl,
            method: 'GET',
            data: params.toString(),
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                if (response.success) {
                    this.renderData(response.data);
                    this.renderPagination(response.pagination, response.links);
                    $(this.options.counterId).text(response.pagination.total);

                    // استدعاء callback إذا كان موجود
                    if (this.options.onDataLoaded) {
                        this.options.onDataLoaded(response);
                    }
                }
            },
            error: (xhr) => {
                console.error('خطأ في تحميل البيانات:', xhr);
                this.renderError();
            },
            complete: () => {
                $(this.options.loadingIndicatorId).addClass('d-none');
            }
        });
    }

    renderData(data) {
        let html = '';
        
        if (data.length === 0) {
            html = this.renderEmptyState();
        } else {
            data.forEach((item) => {
                html += this.renderRow(item);
            });
        }
        
        $(this.options.tableId + ' tbody').html(html);
    }

    renderRow(item) {
        // يجب تخصيص هذه الدالة في كل جدول
        if (this.options.renderRow) {
            return this.options.renderRow(item);
        }
        return '<tr><td colspan="100%">يجب تخصيص دالة renderRow</td></tr>';
    }

    renderEmptyState() {
        const colspan = $(this.options.tableId + ' thead th').length;
        return `
            <tr>
                <td colspan="${colspan}" class="text-center py-4">
                    <div class="text-gray-500">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>لا توجد بيانات</p>
                    </div>
                </td>
            </tr>
        `;
    }

    renderError() {
        const colspan = $(this.options.tableId + ' thead th').length;
        $(this.options.tableId + ' tbody').html(`
            <tr>
                <td colspan="${colspan}" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>حدث خطأ في تحميل البيانات</p>
                </td>
            </tr>
        `);
    }

    renderPagination(pagination, links) {
        if (pagination.last_page <= 1) {
            $(this.options.paginationContainerId).html('');
            return;
        }

        $(this.options.paginationContainerId).html(links);
    }

    // دالة التأخير للبحث
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // إعادة تحميل البيانات
    refresh() {
        this.loadData();
    }

    // تحديث الصفحة الحالية
    setPage(page) {
        this.currentPage = page;
        this.loadData();
    }

    // تحديث الترتيب
    setSort(sortBy, sortDir = 'asc') {
        this.sortBy = sortBy;
        this.sortDir = sortDir;
        this.currentPage = 1;
        this.loadData();
    }
}

// تصدير الكلاس للاستخدام العام
window.AjaxTable = AjaxTable;
