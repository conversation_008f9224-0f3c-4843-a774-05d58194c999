<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdvertisementVideo extends Model
{
    use HasFactory;

    protected $fillable = [
        'advertisement_id',
        'video_path',
        'video_name',
        'thumbnail_path',
        'duration',
    ];

    // العلاقات
    public function advertisement()
    {
        return $this->belongsTo(Advertisement::class);
    }

    // دوال مساعدة
    public function getFullVideoUrl()
    {
        return asset('storage/' . $this->video_path);
    }

    public function getFullThumbnailUrl()
    {
        return $this->thumbnail_path ? asset('storage/' . $this->thumbnail_path) : null;
    }

    public function getFormattedDuration()
    {
        if (!$this->duration) return null;
        
        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }
}
