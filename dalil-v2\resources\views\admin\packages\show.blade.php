@extends('admin.layouts.app')

@section('title', 'تفاصيل الباقة')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تفاصيل الباقة: {{ $package->name }}</h1>
            <p class="text-muted">عرض معلومات وإحصائيات الباقة</p>
        </div>
        <div>
            <a href="{{ route('admin.packages.edit', $package) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>تعديل الباقة
            </a>
            <a href="{{ route('admin.packages.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الباقة -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الباقة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">اسم الباقة:</td>
                                    <td>{{ $package->name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">السعر:</td>
                                    <td>
                                        <span class="badge {{ $package->price > 0 ? 'bg-success' : 'bg-secondary' }} fs-6">
                                            {{ $package->getFormattedPrice() }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">مدة الباقة:</td>
                                    <td>{{ $package->duration_days }} يوم</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">عدد الإعلانات:</td>
                                    <td>{{ $package->max_ads }} إعلان</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الصور لكل إعلان:</td>
                                    <td>{{ $package->max_images_per_ad }} صورة</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">الإعلانات المميزة:</td>
                                    <td>
                                        @if($package->featured_ads)
                                            <span class="badge bg-warning text-dark">متاحة</span>
                                        @else
                                            <span class="badge bg-secondary">غير متاحة</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">السماح بالفيديو:</td>
                                    <td>
                                        @if($package->video_allowed)
                                            <span class="badge bg-info">متاح</span>
                                        @else
                                            <span class="badge bg-secondary">غير متاح</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">ترتيب العرض:</td>
                                    <td>{{ $package->sort_order }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">حالة الباقة:</td>
                                    <td>
                                        @if($package->is_active)
                                            <span class="badge bg-success">نشطة</span>
                                        @else
                                            <span class="badge bg-danger">غير نشطة</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ الإنشاء:</td>
                                    <td>{{ $package->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($package->description)
                    <div class="mt-3">
                        <h6>وصف الباقة:</h6>
                        <p class="text-muted">{{ $package->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- إحصائيات الاشتراكات -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">إحصائيات الاشتراكات</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-primary mb-0">{{ $package->subscriptions()->count() }}</h3>
                                <small class="text-muted">إجمالي الاشتراكات</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-success mb-0">{{ $package->subscriptions()->where('status', 'active')->count() }}</h3>
                                <small class="text-muted">اشتراكات نشطة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-warning mb-0">{{ $package->subscriptions()->where('status', 'expired')->count() }}</h3>
                                <small class="text-muted">اشتراكات منتهية</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-info mb-0">{{ number_format($package->subscriptions()->sum('amount_paid'), 2) }}</h3>
                            <small class="text-muted">إجمالي الإيرادات (ريال)</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر الاشتراكات -->
            @if($package->subscriptions()->count() > 0)
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخر الاشتراكات</h5>
                    <small class="text-muted">آخر 10 اشتراكات</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($package->subscriptions()->with('user')->latest()->limit(10)->get() as $subscription)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ $subscription->user->name }}</div>
                                                <small class="text-muted">{{ $subscription->user->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                    <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                    <td>{{ number_format($subscription->amount, 2) }} ريال</td>
                                    <td>
                                        @switch($subscription->status)
                                            @case('active')
                                                <span class="badge bg-success">نشط</span>
                                                @break
                                            @case('expired')
                                                <span class="badge bg-warning">منتهي</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge bg-danger">ملغي</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $subscription->status }}</span>
                                        @endswitch
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- معاينة الباقة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معاينة الباقة</h5>
                </div>
                <div class="card-body">
                    <div class="border rounded p-3 bg-light">
                        <h6 class="text-primary">{{ $package->name }}</h6>
                        @if($package->description)
                            <p class="text-muted small">{{ $package->description }}</p>
                        @endif
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">السعر:</span>
                            <span class="text-success fw-bold">{{ $package->getFormattedPrice() }}</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>المدة:</span>
                            <span>{{ $package->duration_days }} يوم</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>عدد الإعلانات:</span>
                            <span>{{ $package->max_ads }}</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الصور لكل إعلان:</span>
                            <span>{{ $package->max_images_per_ad }}</span>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">المميزات الإضافية:</small>
                            <div class="mt-1">
                                @if($package->featured_ads || $package->video_allowed)
                                    @if($package->featured_ads)
                                        <span class="badge bg-warning text-dark me-1">مميزة</span>
                                    @endif
                                    @if($package->video_allowed)
                                        <span class="badge bg-info me-1">فيديو</span>
                                    @endif
                                @else
                                    <span class="text-muted">لا توجد مميزات إضافية</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.packages.edit', $package) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>تعديل الباقة
                        </a>
                        
                        <form action="{{ route('admin.packages.toggle-status', $package) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-outline-{{ $package->is_active ? 'warning' : 'success' }} btn-sm w-100">
                                <i class="fas fa-{{ $package->is_active ? 'pause' : 'play' }} me-2"></i>
                                {{ $package->is_active ? 'إيقاف الباقة' : 'تفعيل الباقة' }}
                            </button>
                        </form>

                        @if($package->subscriptions()->where('status', 'active')->count() == 0)
                        <form action="{{ route('admin.packages.destroy', $package) }}" method="POST" 
                              onsubmit="return confirm('هل أنت متأكد من حذف هذه الباقة؟')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash me-2"></i>حذف الباقة
                            </button>
                        </form>
                        @else
                        <button class="btn btn-outline-danger btn-sm w-100" disabled title="لا يمكن حذف باقة لديها اشتراكات نشطة">
                            <i class="fas fa-trash me-2"></i>حذف الباقة
                        </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle text-info me-2"></i>معلومات إضافية</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0 small">
                        <li class="mb-2">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            تم الإنشاء: {{ $package->created_at->format('Y-m-d H:i') }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-edit text-muted me-2"></i>
                            آخر تحديث: {{ $package->updated_at->format('Y-m-d H:i') }}
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-sort text-muted me-2"></i>
                            ترتيب العرض: {{ $package->sort_order }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
