@extends('admin.layouts.app')

@section('title', 'إدارة الأدوار')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
    <li class="breadcrumb-item active">إدارة الأدوار</li>
@endsection

@section('content')
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الأدوار</h1>
        <a href="{{ route('admin.roles.create') }}" class="btn btn-primary btn-sm shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> إضافة دور جديد
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4 filters-card">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
        </div>
        <div class="card-body">
            <form id="filters-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="اسم الدور، الوصف...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">نوع الدور</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">الكل</option>
                                <option value="admin">إداري</option>
                                <option value="user">مستخدم</option>
                                <option value="custom">مخصص</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="per_page">عدد النتائج</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="15">15</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" id="clear-filters" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Roles Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                قائمة الأدوار 
                <span id="roles-count" class="badge badge-primary counter-badge">0</span>
            </h6>
            <div id="loading-indicator" class="d-none">
                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="roles-table" class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="display_name">
                                اسم الدور <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="name">
                                الاسم التقني <i class="fas fa-sort"></i>
                            </th>
                            <th>الوصف</th>
                            <th class="sortable" data-sort="users_count">
                                عدد المستخدمين <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="permissions_count">
                                عدد الصلاحيات <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="created_at">
                                تاريخ الإنشاء <i class="fas fa-sort"></i>
                            </th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="roles-table-body">
                        <!-- سيتم تحميل البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="d-flex justify-content-center mt-3">
                <!-- سيتم تحميل الترقيم عبر AJAX -->
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="{{ asset('js/admin/ajax-table.js') }}"></script>
<script>
$(document).ready(function() {
    // إنشاء جدول الأدوار التفاعلي
    const rolesTable = new AjaxTable({
        tableId: '#roles-table',
        filtersFormId: '#filters-form',
        loadingIndicatorId: '#loading-indicator',
        paginationContainerId: '#pagination-container',
        counterId: '#roles-count',
        dataUrl: '{{ route("admin.roles.data") }}',
        renderRow: renderRoleRow
    });

    // دالة عرض صف الدور
    function renderRoleRow(role) {
        const createdAt = new Date(role.created_at).toLocaleDateString('ar-SA');
        const description = role.description ? role.description.substring(0, 50) + '...' : '-';

        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="mr-3">
                            <div class="icon-circle bg-info">
                                <i class="fas fa-user-tag text-white"></i>
                            </div>
                        </div>
                        <div>
                            <div class="font-weight-bold">${role.display_name}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <code class="text-primary">${role.name}</code>
                </td>
                <td>
                    <span class="text-gray-600" title="${role.description || ''}">${description}</span>
                </td>
                <td>
                    <span class="badge badge-info">${role.users_count}</span>
                </td>
                <td>
                    <span class="badge badge-success">${role.permissions_count}</span>
                </td>
                <td>${createdAt}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown">
                            إجراءات
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="/admin/roles/${role.id}">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <a class="dropdown-item" href="/admin/roles/${role.id}/edit">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <div class="dropdown-divider"></div>
                            <button class="dropdown-item text-info" onclick="managePermissions(${role.id})">
                                <i class="fas fa-key"></i> إدارة الصلاحيات
                            </button>
                            <div class="dropdown-divider"></div>
                            ${role.users_count === 0 ? `
                                <button class="dropdown-item text-danger" onclick="deleteRole(${role.id})">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            ` : `
                                <span class="dropdown-item text-muted">
                                    <i class="fas fa-info-circle"></i> لا يمكن الحذف (مرتبط بمستخدمين)
                                </span>
                            `}
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    // دالة إدارة الصلاحيات
    window.managePermissions = function(roleId) {
        window.location.href = `/admin/roles/${roleId}/permissions`;
    };

    // دالة حذف الدور
    window.deleteRole = function(roleId) {
        if (!confirm('هل أنت متأكد من حذف هذا الدور؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            return;
        }

        $.ajax({
            url: `/admin/roles/${roleId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    rolesTable.refresh();
                    showAlert('success', response.message);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showAlert('error', response?.message || 'حدث خطأ أثناء حذف الدور');
            }
        });
    };

    // دالة عرض التنبيهات
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.content').prepend(alertHtml);
        
        // إخفاء التنبيه بعد 5 ثوان
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

code {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    background-color: #f8f9fc;
}

.counter-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
}
</style>
@endpush
