<?php $__env->startSection('title', 'إدارة الصلاحيات'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">الرئيسية</a></li>
    <li class="breadcrumb-item active">إدارة الصلاحيات</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الصلاحيات</h1>
        <div>
            <button type="button" class="btn btn-warning btn-sm shadow-sm" onclick="createDefaultPermissions()">
                <i class="fas fa-magic fa-sm text-white-50"></i> إنشاء الصلاحيات الافتراضية
            </button>
            <a href="<?php echo e(route('admin.permissions.create')); ?>" class="btn btn-primary btn-sm shadow-sm">
                <i class="fas fa-plus fa-sm text-white-50"></i> إضافة صلاحية جديدة
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">فلاتر البحث</h6>
        </div>
        <div class="card-body">
            <form id="filters-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="البحث في اسم الصلاحية أو الوصف...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="group">المجموعة</label>
                            <select class="form-control" id="group" name="group">
                                <option value="">جميع المجموعات</option>
                                <option value="المستخدمين">المستخدمين</option>
                                <option value="الأدوار والصلاحيات">الأدوار والصلاحيات</option>
                                <option value="الإعلانات">الإعلانات</option>
                                <option value="الباقات والاشتراكات">الباقات والاشتراكات</option>
                                <option value="البيانات الأساسية">البيانات الأساسية</option>
                                <option value="الإعدادات">الإعدادات</option>
                                <option value="التقارير والإحصائيات">التقارير والإحصائيات</option>
                                <option value="عام">عام</option>
                                <option value="النظام">النظام</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" class="btn btn-primary me-2" onclick="applyFilters()">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-times"></i> مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الصلاحيات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الصلاحيات</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="permissions-table">
                    <thead>
                        <tr>
                            <th data-sort="id">#</th>
                            <th data-sort="display_name">اسم الصلاحية</th>
                            <th data-sort="name">الاسم التقني</th>
                            <th data-sort="group">المجموعة</th>
                            <th>الوصف</th>
                            <th data-sort="roles_count">الأدوار المرتبطة</th>
                            <th data-sort="created_at">تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تحميل البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div id="table-info"></div>
                <nav id="table-pagination"></nav>
            </div>
        </div>
    </div>

    <!-- نموذج تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد حذف الصلاحية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من رغبتك في حذف هذه الصلاحية؟</p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> سيتم إزالة هذه الصلاحية من جميع الأدوار المرتبطة بها!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete">حذف الصلاحية</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // إعداد جدول AJAX
    const permissionsTable = new AjaxTable({
        tableId: '#permissions-table',
        dataUrl: '<?php echo e(route("admin.permissions.data")); ?>',
        renderRow: renderPermissionRow,
        defaultSort: 'group',
        defaultOrder: 'asc'
    });

    // تطبيق الفلاتر
    window.applyFilters = function() {
        const filters = {
            search: $('#search').val(),
            group: $('#group').val()
        };
        permissionsTable.applyFilters(filters);
    };

    // مسح الفلاتر
    window.clearFilters = function() {
        $('#filters-form')[0].reset();
        permissionsTable.clearFilters();
    };

    // البحث التلقائي
    $('#search').on('keyup', debounce(function() {
        applyFilters();
    }, 500));

    $('#group').on('change', function() {
        applyFilters();
    });

    // تأكيد الحذف
    let deleteUrl = '';
    $(document).on('click', '.delete-permission', function() {
        deleteUrl = $(this).data('url');
        $('#deleteModal').modal('show');
    });

    $('#confirm-delete').on('click', function() {
        if (deleteUrl) {
            $.ajax({
                url: deleteUrl,
                type: 'DELETE',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    $('#deleteModal').modal('hide');
                    showAlert('success', response.message || 'تم حذف الصلاحية بنجاح');
                    permissionsTable.reload();
                },
                error: function(xhr) {
                    $('#deleteModal').modal('hide');
                    const message = xhr.responseJSON?.message || 'حدث خطأ أثناء حذف الصلاحية';
                    showAlert('error', message);
                }
            });
        }
    });

    // إنشاء الصلاحيات الافتراضية
    window.createDefaultPermissions = function() {
        if (confirm('هل تريد إنشاء الصلاحيات الافتراضية؟ سيتم إنشاء الصلاحيات المفقودة فقط.')) {
            $.ajax({
                url: '<?php echo e(route("admin.permissions.create-defaults")); ?>',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    showAlert('success', response.message || 'تم إنشاء الصلاحيات الافتراضية بنجاح');
                    permissionsTable.reload();
                },
                error: function(xhr) {
                    const message = xhr.responseJSON?.message || 'حدث خطأ أثناء إنشاء الصلاحيات';
                    showAlert('error', message);
                }
            });
        }
    };
});

// دالة رسم صف الصلاحية
function renderPermissionRow(permission) {
    const rolesCount = permission.roles_count || 0;
    const canDelete = rolesCount === 0;
    
    return `
        <tr>
            <td>${permission.id}</td>
            <td>
                <strong>${permission.display_name}</strong>
            </td>
            <td>
                <code>${permission.name}</code>
            </td>
            <td>
                <span class="badge badge-info">${permission.group}</span>
            </td>
            <td>
                ${permission.description ? `<small class="text-muted">${permission.description}</small>` : '-'}
            </td>
            <td>
                <span class="badge badge-${rolesCount > 0 ? 'success' : 'secondary'}">
                    ${rolesCount} دور
                </span>
            </td>
            <td>
                <small>${formatDate(permission.created_at)}</small>
            </td>
            <td>
                <div class="btn-group" role="group">
                    <a href="/admin/permissions/${permission.id}" 
                       class="btn btn-sm btn-outline-info" title="عرض">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="/admin/permissions/${permission.id}/edit" 
                       class="btn btn-sm btn-outline-primary" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    ${canDelete ? `
                        <button type="button" 
                                class="btn btn-sm btn-outline-danger delete-permission" 
                                data-url="/admin/permissions/${permission.id}"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : `
                        <button type="button" 
                                class="btn btn-sm btn-outline-danger" 
                                disabled
                                title="لا يمكن حذف الصلاحية لوجود أدوار مرتبطة بها">
                            <i class="fas fa-trash"></i>
                        </button>
                    `}
                </div>
            </td>
        </tr>
    `;
}

// دالة تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// دالة عرض التنبيهات
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى الصفحة
    $('.content-wrapper').prepend(alertHtml);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>

<style>
.badge {
    font-size: 0.75em;
    padding: 6px 12px;
    border-radius: 20px;
}

.table th[data-sort] {
    cursor: pointer;
    user-select: none;
}

.table th[data-sort]:hover {
    background-color: #f8f9fa;
}

.table th[data-sort].sort-asc::after {
    content: " ↑";
    color: #007bff;
}

.table th[data-sort].sort-desc::after {
    content: " ↓";
    color: #007bff;
}

.btn-group .btn {
    margin-left: 2px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.875em;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/permissions/index.blade.php ENDPATH**/ ?>