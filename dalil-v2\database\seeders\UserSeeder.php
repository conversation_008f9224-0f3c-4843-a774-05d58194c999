<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // إنشاء مستخدمين تجريبيين للاختبار
        $users = [
            [
                'name' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'phone' => '777123456',
                'password' => Hash::make('password'),
                'user_type' => 'individual',
                'status' => 'active',
                'city' => 'صنعاء',
                'address' => 'شارع الزبيري',
            ],
            [
                'name' => 'معرض النجوم للسيارات',
                'email' => '<EMAIL>',
                'phone' => '777234567',
                'password' => Hash::make('password'),
                'user_type' => 'showroom',
                'status' => 'active',
                'city' => 'عدن',
                'address' => 'شارع المعلا',
            ],
            [
                'name' => 'فاطمة علي',
                'email' => '<EMAIL>',
                'phone' => '777345678',
                'password' => Hash::make('password'),
                'user_type' => 'individual',
                'status' => 'active',
                'city' => 'تعز',
                'address' => 'شارع جمال',
            ],
            [
                'name' => 'معرض الأصالة',
                'email' => '<EMAIL>',
                'phone' => '777456789',
                'password' => Hash::make('password'),
                'user_type' => 'showroom',
                'status' => 'active',
                'city' => 'الحديدة',
                'address' => 'شارع الكورنيش',
            ],
            [
                'name' => 'محمد سالم',
                'email' => '<EMAIL>',
                'phone' => '777567890',
                'password' => Hash::make('password'),
                'user_type' => 'individual',
                'status' => 'active',
                'city' => 'إب',
                'address' => 'شارع السبعين',
            ],
            [
                'name' => 'معرض الفخامة',
                'email' => '<EMAIL>',
                'phone' => '777678901',
                'password' => Hash::make('password'),
                'user_type' => 'showroom',
                'status' => 'active',
                'city' => 'صنعاء',
                'address' => 'شارع الستين',
            ],
            [
                'name' => 'سارة أحمد',
                'email' => '<EMAIL>',
                'phone' => '777789012',
                'password' => Hash::make('password'),
                'user_type' => 'individual',
                'status' => 'active',
                'city' => 'عدن',
                'address' => 'شارع المنصورة',
            ],
            [
                'name' => 'عبدالله حسن',
                'email' => '<EMAIL>',
                'phone' => '777890123',
                'password' => Hash::make('password'),
                'user_type' => 'individual',
                'status' => 'inactive',
                'city' => 'تعز',
                'address' => 'شارع الثورة',
            ],
            [
                'name' => 'معرض الحديث',
                'email' => '<EMAIL>',
                'phone' => '777901234',
                'password' => Hash::make('password'),
                'user_type' => 'showroom',
                'status' => 'active',
                'city' => 'الحديدة',
                'address' => 'شارع الجمهورية',
            ],
            [
                'name' => 'نادية محمد',
                'email' => '<EMAIL>',
                'phone' => '777012345',
                'password' => Hash::make('password'),
                'user_type' => 'individual',
                'status' => 'active',
                'city' => 'إب',
                'address' => 'شارع الوحدة',
            ],
        ];

        foreach ($users as $userData) {
            // التحقق من عدم وجود المستخدم مسبقاً
            if (!User::where('email', $userData['email'])->exists()) {
                User::create($userData);
            }
        }

        $this->command->info('تم إنشاء ' . count($users) . ' مستخدم تجريبي بنجاح');
    }
}
