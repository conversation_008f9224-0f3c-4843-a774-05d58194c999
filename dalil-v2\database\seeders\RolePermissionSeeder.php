<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // إدارة المستخدمين
            ['name' => 'users.view', 'display_name' => 'عرض المستخدمين', 'description' => 'عرض قائمة المستخدمين', 'group' => 'المستخدمين'],
            ['name' => 'users.create', 'display_name' => 'إنشاء مستخدم', 'description' => 'إنشاء مستخدم جديد', 'group' => 'المستخدمين'],
            ['name' => 'users.edit', 'display_name' => 'تعديل المستخدمين', 'description' => 'تعديل بيانات المستخدمين', 'group' => 'المستخدمين'],
            ['name' => 'users.delete', 'display_name' => 'حذف المستخدمين', 'description' => 'حذف المستخدمين', 'group' => 'المستخدمين'],
            ['name' => 'users.suspend', 'display_name' => 'تعليق المستخدمين', 'description' => 'تعليق حسابات المستخدمين', 'group' => 'المستخدمين'],

            // إدارة الأدوار والصلاحيات
            ['name' => 'roles.view', 'display_name' => 'عرض الأدوار', 'description' => 'عرض قائمة الأدوار', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'roles.create', 'display_name' => 'إنشاء دور', 'description' => 'إنشاء دور جديد', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'roles.edit', 'display_name' => 'تعديل الأدوار', 'description' => 'تعديل الأدوار والصلاحيات', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'roles.delete', 'display_name' => 'حذف الأدوار', 'description' => 'حذف الأدوار', 'group' => 'الأدوار والصلاحيات'],

            // إدارة الإعلانات
            ['name' => 'ads.view', 'display_name' => 'عرض الإعلانات', 'description' => 'عرض جميع الإعلانات', 'group' => 'الإعلانات'],
            ['name' => 'ads.approve', 'display_name' => 'الموافقة على الإعلانات', 'description' => 'الموافقة على الإعلانات المعلقة', 'group' => 'الإعلانات'],
            ['name' => 'ads.reject', 'display_name' => 'رفض الإعلانات', 'description' => 'رفض الإعلانات غير المناسبة', 'group' => 'الإعلانات'],
            ['name' => 'ads.delete', 'display_name' => 'حذف الإعلانات', 'description' => 'حذف الإعلانات', 'group' => 'الإعلانات'],
            ['name' => 'ads.feature', 'display_name' => 'تمييز الإعلانات', 'description' => 'جعل الإعلانات مميزة', 'group' => 'الإعلانات'],

            // إدارة الباقات
            ['name' => 'packages.view', 'display_name' => 'عرض الباقات', 'description' => 'عرض قائمة الباقات', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'packages.create', 'display_name' => 'إنشاء باقة', 'description' => 'إنشاء باقة جديدة', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'packages.edit', 'display_name' => 'تعديل الباقات', 'description' => 'تعديل الباقات الموجودة', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'packages.delete', 'display_name' => 'حذف الباقات', 'description' => 'حذف الباقات', 'group' => 'الباقات والاشتراكات'],

            // إدارة الاشتراكات
            ['name' => 'subscriptions.view', 'display_name' => 'عرض الاشتراكات', 'description' => 'عرض جميع الاشتراكات', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'subscriptions.create', 'display_name' => 'إنشاء اشتراك', 'description' => 'إنشاء اشتراك جديد', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'subscriptions.edit', 'display_name' => 'تعديل الاشتراكات', 'description' => 'تعديل الاشتراكات', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'subscriptions.cancel', 'display_name' => 'إلغاء الاشتراكات', 'description' => 'إلغاء الاشتراكات', 'group' => 'الباقات والاشتراكات'],

            // إدارة البيانات الأساسية
            ['name' => 'basic_data.view', 'display_name' => 'عرض البيانات الأساسية', 'description' => 'عرض الشركات والموديلات والمحافظات', 'group' => 'البيانات الأساسية'],
            ['name' => 'basic_data.create', 'display_name' => 'إنشاء بيانات أساسية', 'description' => 'إضافة شركات وموديلات جديدة', 'group' => 'البيانات الأساسية'],
            ['name' => 'basic_data.edit', 'display_name' => 'تعديل البيانات الأساسية', 'description' => 'تعديل البيانات الأساسية', 'group' => 'البيانات الأساسية'],
            ['name' => 'basic_data.delete', 'display_name' => 'حذف البيانات الأساسية', 'description' => 'حذف البيانات الأساسية', 'group' => 'البيانات الأساسية'],

            // إدارة الإعدادات
            ['name' => 'settings.view', 'display_name' => 'عرض الإعدادات', 'description' => 'عرض إعدادات النظام', 'group' => 'الإعدادات'],
            ['name' => 'settings.edit', 'display_name' => 'تعديل الإعدادات', 'description' => 'تعديل إعدادات النظام', 'group' => 'الإعدادات'],

            // التقارير والإحصائيات
            ['name' => 'reports.view', 'display_name' => 'عرض التقارير', 'description' => 'عرض التقارير والإحصائيات', 'group' => 'التقارير والإحصائيات'],
            ['name' => 'reports.export', 'display_name' => 'تصدير التقارير', 'description' => 'تصدير التقارير', 'group' => 'التقارير والإحصائيات'],

            // إدارة الرسائل
            ['name' => 'messages.view', 'display_name' => 'عرض الرسائل', 'description' => 'عرض جميع الرسائل', 'group' => 'عام'],
            ['name' => 'messages.delete', 'display_name' => 'حذف الرسائل', 'description' => 'حذف الرسائل غير المناسبة', 'group' => 'عام'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // إنشاء الأدوار
        $superAdmin = Role::create([
            'name' => 'super_admin',
            'display_name' => 'مدير عام',
            'description' => 'مدير عام للنظام مع جميع الصلاحيات'
        ]);

        $admin = Role::create([
            'name' => 'admin',
            'display_name' => 'مدير',
            'description' => 'مدير النظام'
        ]);

        $moderator = Role::create([
            'name' => 'moderator',
            'display_name' => 'مشرف',
            'description' => 'مشرف على المحتوى'
        ]);

        // ربط الصلاحيات بالأدوار
        $allPermissions = Permission::all();
        $superAdmin->permissions()->attach($allPermissions);

        // صلاحيات المدير
        $adminPermissions = Permission::whereIn('group', [
            'المستخدمين', 'الإعلانات', 'الباقات والاشتراكات', 'البيانات الأساسية', 'التقارير والإحصائيات', 'عام'
        ])->get();
        $admin->permissions()->attach($adminPermissions);

        // صلاحيات المشرف
        $moderatorPermissions = Permission::whereIn('name', [
            'ads.view', 'ads.approve', 'ads.reject', 'ads.feature',
            'messages.view', 'messages.delete',
            'users.view', 'users.suspend'
        ])->get();
        $moderator->permissions()->attach($moderatorPermissions);
    }
}
