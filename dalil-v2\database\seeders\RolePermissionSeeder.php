<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // إدارة المستخدمين
            ['name' => 'users.view', 'display_name' => 'عرض المستخدمين', 'description' => 'عرض قائمة المستخدمين', 'module' => 'users'],
            ['name' => 'users.create', 'display_name' => 'إنشاء مستخدم', 'description' => 'إنشاء مستخدم جديد', 'module' => 'users'],
            ['name' => 'users.edit', 'display_name' => 'تعديل المستخدمين', 'description' => 'تعديل بيانات المستخدمين', 'module' => 'users'],
            ['name' => 'users.delete', 'display_name' => 'حذف المستخدمين', 'description' => 'حذف المستخدمين', 'module' => 'users'],
            ['name' => 'users.suspend', 'display_name' => 'تعليق المستخدمين', 'description' => 'تعليق حسابات المستخدمين', 'module' => 'users'],

            // إدارة الأدوار والصلاحيات
            ['name' => 'roles.view', 'display_name' => 'عرض الأدوار', 'description' => 'عرض قائمة الأدوار', 'module' => 'roles'],
            ['name' => 'roles.create', 'display_name' => 'إنشاء دور', 'description' => 'إنشاء دور جديد', 'module' => 'roles'],
            ['name' => 'roles.edit', 'display_name' => 'تعديل الأدوار', 'description' => 'تعديل الأدوار والصلاحيات', 'module' => 'roles'],
            ['name' => 'roles.delete', 'display_name' => 'حذف الأدوار', 'description' => 'حذف الأدوار', 'module' => 'roles'],

            // إدارة الإعلانات
            ['name' => 'ads.view', 'display_name' => 'عرض الإعلانات', 'description' => 'عرض جميع الإعلانات', 'module' => 'ads'],
            ['name' => 'ads.approve', 'display_name' => 'الموافقة على الإعلانات', 'description' => 'الموافقة على الإعلانات المعلقة', 'module' => 'ads'],
            ['name' => 'ads.reject', 'display_name' => 'رفض الإعلانات', 'description' => 'رفض الإعلانات غير المناسبة', 'module' => 'ads'],
            ['name' => 'ads.delete', 'display_name' => 'حذف الإعلانات', 'description' => 'حذف الإعلانات', 'module' => 'ads'],
            ['name' => 'ads.feature', 'display_name' => 'تمييز الإعلانات', 'description' => 'جعل الإعلانات مميزة', 'module' => 'ads'],

            // إدارة الباقات
            ['name' => 'packages.view', 'display_name' => 'عرض الباقات', 'description' => 'عرض قائمة الباقات', 'module' => 'packages'],
            ['name' => 'packages.create', 'display_name' => 'إنشاء باقة', 'description' => 'إنشاء باقة جديدة', 'module' => 'packages'],
            ['name' => 'packages.edit', 'display_name' => 'تعديل الباقات', 'description' => 'تعديل الباقات الموجودة', 'module' => 'packages'],
            ['name' => 'packages.delete', 'display_name' => 'حذف الباقات', 'description' => 'حذف الباقات', 'module' => 'packages'],

            // إدارة الاشتراكات
            ['name' => 'subscriptions.view', 'display_name' => 'عرض الاشتراكات', 'description' => 'عرض جميع الاشتراكات', 'module' => 'subscriptions'],
            ['name' => 'subscriptions.create', 'display_name' => 'إنشاء اشتراك', 'description' => 'إنشاء اشتراك جديد', 'module' => 'subscriptions'],
            ['name' => 'subscriptions.edit', 'display_name' => 'تعديل الاشتراكات', 'description' => 'تعديل الاشتراكات', 'module' => 'subscriptions'],
            ['name' => 'subscriptions.cancel', 'display_name' => 'إلغاء الاشتراكات', 'description' => 'إلغاء الاشتراكات', 'module' => 'subscriptions'],

            // إدارة البيانات الأساسية
            ['name' => 'basic_data.view', 'display_name' => 'عرض البيانات الأساسية', 'description' => 'عرض الشركات والموديلات والمحافظات', 'module' => 'basic_data'],
            ['name' => 'basic_data.create', 'display_name' => 'إنشاء بيانات أساسية', 'description' => 'إضافة شركات وموديلات جديدة', 'module' => 'basic_data'],
            ['name' => 'basic_data.edit', 'display_name' => 'تعديل البيانات الأساسية', 'description' => 'تعديل البيانات الأساسية', 'module' => 'basic_data'],
            ['name' => 'basic_data.delete', 'display_name' => 'حذف البيانات الأساسية', 'description' => 'حذف البيانات الأساسية', 'module' => 'basic_data'],

            // إدارة الإعدادات
            ['name' => 'settings.view', 'display_name' => 'عرض الإعدادات', 'description' => 'عرض إعدادات النظام', 'module' => 'settings'],
            ['name' => 'settings.edit', 'display_name' => 'تعديل الإعدادات', 'description' => 'تعديل إعدادات النظام', 'module' => 'settings'],

            // التقارير والإحصائيات
            ['name' => 'reports.view', 'display_name' => 'عرض التقارير', 'description' => 'عرض التقارير والإحصائيات', 'module' => 'reports'],
            ['name' => 'reports.export', 'display_name' => 'تصدير التقارير', 'description' => 'تصدير التقارير', 'module' => 'reports'],

            // إدارة الرسائل
            ['name' => 'messages.view', 'display_name' => 'عرض الرسائل', 'description' => 'عرض جميع الرسائل', 'module' => 'messages'],
            ['name' => 'messages.delete', 'display_name' => 'حذف الرسائل', 'description' => 'حذف الرسائل غير المناسبة', 'module' => 'messages'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // إنشاء الأدوار
        $superAdmin = Role::create([
            'name' => 'super_admin',
            'display_name' => 'مدير عام',
            'description' => 'مدير عام للنظام مع جميع الصلاحيات'
        ]);

        $admin = Role::create([
            'name' => 'admin',
            'display_name' => 'مدير',
            'description' => 'مدير النظام'
        ]);

        $moderator = Role::create([
            'name' => 'moderator',
            'display_name' => 'مشرف',
            'description' => 'مشرف على المحتوى'
        ]);

        // ربط الصلاحيات بالأدوار
        $allPermissions = Permission::all();
        $superAdmin->permissions()->attach($allPermissions);

        // صلاحيات المدير
        $adminPermissions = Permission::whereIn('module', [
            'users', 'ads', 'packages', 'subscriptions', 'basic_data', 'reports', 'messages'
        ])->get();
        $admin->permissions()->attach($adminPermissions);

        // صلاحيات المشرف
        $moderatorPermissions = Permission::whereIn('name', [
            'ads.view', 'ads.approve', 'ads.reject', 'ads.feature',
            'messages.view', 'messages.delete',
            'users.view', 'users.suspend'
        ])->get();
        $moderator->permissions()->attach($moderatorPermissions);
    }
}
