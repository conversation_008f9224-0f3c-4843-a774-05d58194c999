@extends('admin.layouts.app')

@section('title', 'تفاصيل الدور: ' . $role->display_name)

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.roles.index') }}">إدارة الأدوار</a></li>
    <li class="breadcrumb-item active">تفاصيل الدور</li>
@endsection

@section('content')
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل الدور: {{ $role->display_name }}</h1>
        <div>
            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-primary btn-sm shadow-sm">
                <i class="fas fa-edit fa-sm text-white-50"></i> تعديل
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm shadow-sm">
                <i class="fas fa-arrow-right fa-sm text-white-50"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- معلومات الدور -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الدور</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">اسم الدور:</label>
                                <p class="text-gray-800">{{ $role->display_name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">الاسم التقني:</label>
                                <p class="text-gray-800"><code>{{ $role->name }}</code></p>
                            </div>
                        </div>
                    </div>

                    @if($role->description)
                        <div class="form-group">
                            <label class="font-weight-bold">الوصف:</label>
                            <p class="text-gray-800">{{ $role->description }}</p>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">تاريخ الإنشاء:</label>
                                <p class="text-gray-800">{{ $role->created_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">آخر تحديث:</label>
                                <p class="text-gray-800">{{ $role->updated_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- صلاحيات الدور -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">صلاحيات الدور ({{ $role->permissions->count() }})</h6>
                </div>
                <div class="card-body">
                    @if($role->permissions->count() > 0)
                        @foreach($role->permissions->groupBy('group') as $group => $groupPermissions)
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0 text-primary">
                                        <i class="fas fa-folder"></i> {{ $group }}
                                        <span class="badge badge-primary float-end">{{ $groupPermissions->count() }}</span>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        @foreach($groupPermissions as $permission)
                                            <div class="col-md-6 col-lg-4 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <div>
                                                        <strong>{{ $permission->display_name }}</strong>
                                                        @if($permission->description)
                                                            <br><small class="text-muted">{{ $permission->description }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            لا توجد صلاحيات مرتبطة بهذا الدور حالياً.
                        </div>
                    @endif
                </div>
            </div>

            <!-- المستخدمون المرتبطون بالدور -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">المستخدمون المرتبطون بالدور ({{ $role->users->count() }})</h6>
                </div>
                <div class="card-body">
                    @if($role->users->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الانضمام</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($role->users as $user)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-2">
                                                        <i class="fas fa-user-circle fa-2x text-gray-300"></i>
                                                    </div>
                                                    <div>
                                                        <strong>{{ $user->name }}</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $user->email }}</td>
                                            <td>
                                                @if($user->status === 'active')
                                                    <span class="badge badge-success">نشط</span>
                                                @elseif($user->status === 'suspended')
                                                    <span class="badge badge-warning">معلق</span>
                                                @else
                                                    <span class="badge badge-secondary">غير نشط</span>
                                                @endif
                                            </td>
                                            <td>{{ $user->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <a href="{{ route('admin.users.show', $user) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا يوجد مستخدمون مرتبطون بهذا الدور حالياً.
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- إحصائيات الدور -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إحصائيات الدور</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h3 class="text-primary">{{ $role->users->count() }}</h3>
                            <p class="text-muted mb-0">مستخدم</p>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success">{{ $role->permissions->count() }}</h3>
                            <p class="text-muted mb-0">صلاحية</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-info">{{ $role->users->where('status', 'active')->count() }}</h4>
                            <p class="text-muted mb-0">مستخدم نشط</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ $role->users->where('status', 'suspended')->count() }}</h4>
                            <p class="text-muted mb-0">مستخدم معلق</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل الدور
                        </a>
                        
                        @if($role->users->count() == 0)
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash"></i> حذف الدور
                            </button>
                        @else
                            <button type="button" class="btn btn-danger" disabled title="لا يمكن حذف الدور لوجود مستخدمين مرتبطين به">
                                <i class="fas fa-trash"></i> حذف الدور
                            </button>
                        @endif

                        <a href="{{ route('admin.roles.create') }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> إنشاء دور جديد
                        </a>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">معلومات إضافية</h6>
                </div>
                <div class="card-body">
                    <div class="text-muted">
                        <small>
                            <strong>معرف الدور:</strong> {{ $role->id }}<br>
                            <strong>الاسم التقني:</strong> <code>{{ $role->name }}</code><br>
                            <strong>تاريخ الإنشاء:</strong> {{ $role->created_at->diffForHumans() }}<br>
                            <strong>آخر تحديث:</strong> {{ $role->updated_at->diffForHumans() }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد حذف الدور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من رغبتك في حذف الدور "<strong>{{ $role->display_name }}</strong>"؟</p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form action="{{ route('admin.roles.destroy', $role) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">حذف الدور</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function confirmDelete() {
    $('#deleteModal').modal('show');
}
</script>

<style>
.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge {
    font-size: 0.75em;
    padding: 6px 12px;
    border-radius: 20px;
}

.card .card-header h6 {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}
</style>
@endpush
