<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Permission;

try {
    echo "اختبار API الصلاحيات...\n";
    
    // محاكاة الـ request
    $query = Permission::withCount('roles');
    
    // الترتيب
    $sortBy = 'group';
    $sortDir = 'asc';
    
    if (in_array($sortBy, ['id', 'name', 'display_name', 'group', 'created_at'])) {
        $query->orderBy($sortBy, $sortDir);
    }
    
    // الحصول على البيانات
    $perPage = 15;
    $permissions = $query->paginate($perPage);
    
    echo "إجمالي الصلاحيات: " . $permissions->total() . "\n";
    echo "الصفحة الحالية: " . $permissions->currentPage() . "\n";
    echo "عدد العناصر في الصفحة: " . $permissions->count() . "\n";
    
    echo "\nأول 5 صلاحيات:\n";
    foreach ($permissions->take(5) as $permission) {
        echo "- {$permission->id}: {$permission->display_name} ({$permission->name}) - {$permission->group} - أدوار: {$permission->roles_count}\n";
    }
    
    // تحويل إلى JSON كما يفعل الـ controller
    $response = [
        'data' => $permissions->items(),
        'pagination' => [
            'current_page' => $permissions->currentPage(),
            'last_page' => $permissions->lastPage(),
            'per_page' => $permissions->perPage(),
            'total' => $permissions->total(),
            'from' => $permissions->firstItem(),
            'to' => $permissions->lastItem(),
        ]
    ];
    
    echo "\nJSON Response structure:\n";
    echo "- data count: " . count($response['data']) . "\n";
    echo "- pagination: " . json_encode($response['pagination'], JSON_UNESCAPED_UNICODE) . "\n";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "\n";
}
