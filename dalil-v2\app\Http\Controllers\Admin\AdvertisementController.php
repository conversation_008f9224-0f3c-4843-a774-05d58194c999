<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\User;
use Illuminate\Http\Request;

class AdvertisementController extends Controller
{
    /**
     * عرض قائمة الإعلانات
     */
    public function index(Request $request)
    {
        // إذا كان الطلب AJAX، إرجاع البيانات فقط
        if ($request->ajax()) {
            return $this->getAdvertisementsData($request);
        }

        return view('admin.advertisements.index');
    }

    /**
     * جلب بيانات الإعلانات عبر AJAX
     */
    public function getAdvertisementsData(Request $request)
    {
        $query = Advertisement::with(['user', 'category']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب الفئة
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // فلترة حسب المستخدم
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDir = $request->get('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        $advertisements = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $advertisements->items(),
            'pagination' => [
                'current_page' => $advertisements->currentPage(),
                'last_page' => $advertisements->lastPage(),
                'per_page' => $advertisements->perPage(),
                'total' => $advertisements->total(),
                'from' => $advertisements->firstItem(),
                'to' => $advertisements->lastItem(),
            ],
            'links' => $advertisements->appends(request()->query())->links()->render()
        ]);
    }

    /**
     * عرض تفاصيل الإعلان
     */
    public function show(Advertisement $advertisement)
    {
        $advertisement->load(['user', 'category', 'images']);
        return view('admin.advertisements.show', compact('advertisement'));
    }

    /**
     * تحديث حالة الإعلان
     */
    public function updateStatus(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'status' => 'required|in:pending,active,rejected,expired'
        ]);

        $advertisement->update([
            'status' => $request->status,
            'rejection_reason' => $request->status === 'rejected' ? $request->rejection_reason : null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة الإعلان بنجاح'
        ]);
    }

    /**
     * حذف الإعلان
     */
    public function destroy(Advertisement $advertisement)
    {
        try {
            $advertisement->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'تم حذف الإعلان بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الإعلان'
            ], 500);
        }
    }
}
