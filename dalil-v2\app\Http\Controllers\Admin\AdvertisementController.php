<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\User;
use App\Models\Manufacturer;
use App\Models\CarModel;
use App\Models\Governorate;
use App\Models\District;
use App\Models\FuelType;
use App\Models\TransmissionType;
use App\Models\Color;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdvertisementController extends Controller
{
    /**
     * عرض قائمة الإعلانات
     */
    public function index(Request $request)
    {
        // إذا كان الطلب AJAX، إرجاع البيانات فقط
        if ($request->ajax()) {
            return $this->getAdvertisementsData($request);
        }

        // حساب الإحصائيات
        $stats = $this->getAdvertisementStats();

        // جلب البيانات المرجعية للفلاتر
        $manufacturers = Manufacturer::orderBy('name')->get();
        $governorates = Governorate::orderBy('name')->get();
        $users = User::whereHas('advertisements')->orderBy('name')->get();

        return view('admin.advertisements.index', compact('stats', 'manufacturers', 'governorates', 'users'));
    }

    /**
     * جلب بيانات الإعلانات عبر AJAX
     */
    public function getAdvertisementsData(Request $request)
    {
        $query = Advertisement::with(['user', 'manufacturer', 'model', 'governorate', 'images']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب الشركة المصنعة
        if ($request->filled('manufacturer_id')) {
            $query->where('manufacturer_id', $request->manufacturer_id);
        }

        // فلترة حسب المحافظة
        if ($request->filled('governorate_id')) {
            $query->where('governorate_id', $request->governorate_id);
        }

        // فلترة حسب المستخدم
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // فلترة حسب السعر
        if ($request->filled('price_from')) {
            $query->where('price', '>=', $request->price_from);
        }
        if ($request->filled('price_to')) {
            $query->where('price', '<=', $request->price_to);
        }

        // فلترة حسب السنة
        if ($request->filled('year_from')) {
            $query->where('year', '>=', $request->year_from);
        }
        if ($request->filled('year_to')) {
            $query->where('year', '<=', $request->year_to);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDir = $request->get('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        $advertisements = $query->paginate($request->get('per_page', 15));

        // تحضير البيانات للعرض
        $data = $advertisements->getCollection()->map(function ($ad) {
            return [
                'id' => $ad->id,
                'title' => $ad->title,
                'description' => $ad->description,
                'price' => $ad->price,
                'formatted_price' => $ad->getFormattedPrice(),
                'year' => $ad->year,
                'status' => $ad->status,
                'views_count' => $ad->views_count,
                'favorites_count' => $ad->favorites_count,
                'is_featured' => $ad->is_featured,
                'created_at' => $ad->created_at->format('Y-m-d H:i'),
                'expires_at' => $ad->expires_at ? $ad->expires_at->format('Y-m-d') : null,
                'user' => [
                    'id' => $ad->user->id,
                    'name' => $ad->user->name,
                    'email' => $ad->user->email,
                ],
                'manufacturer' => $ad->manufacturer ? $ad->manufacturer->name : null,
                'model' => $ad->model ? $ad->model->name : null,
                'governorate' => $ad->governorate ? $ad->governorate->name : null,
                'main_image' => $ad->getMainImage() ? $ad->getMainImage()->getFullUrl() : null,
                'images_count' => $ad->images->count(),
                'actions' => $this->getAdvertisementActions($ad),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $advertisements->currentPage(),
                'last_page' => $advertisements->lastPage(),
                'per_page' => $advertisements->perPage(),
                'total' => $advertisements->total(),
                'from' => $advertisements->firstItem(),
                'to' => $advertisements->lastItem(),
            ]
        ]);
    }

    /**
     * عرض تفاصيل الإعلان
     */
    public function show(Advertisement $advertisement)
    {
        $advertisement->load([
            'user',
            'manufacturer',
            'model',
            'governorate',
            'district',
            'fuelType',
            'transmissionType',
            'color',
            'images' => function($query) {
                $query->orderBy('sort_order');
            },
            'videos'
        ]);

        return view('admin.advertisements.show', compact('advertisement'));
    }

    /**
     * تحديث حالة الإعلان
     */
    public function updateStatus(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'status' => 'required|in:pending,approved,rejected,expired',
            'rejection_reason' => 'required_if:status,rejected|string|max:500'
        ], [
            'status.required' => 'حالة الإعلان مطلوبة',
            'status.in' => 'حالة الإعلان غير صحيحة',
            'rejection_reason.required_if' => 'سبب الرفض مطلوب عند رفض الإعلان',
            'rejection_reason.max' => 'سبب الرفض يجب ألا يتجاوز 500 حرف'
        ]);

        $updateData = [
            'status' => $request->status,
            'rejection_reason' => $request->status === 'rejected' ? $request->rejection_reason : null
        ];

        // إضافة تاريخ الموافقة عند الموافقة
        if ($request->status === 'approved' && !$advertisement->approved_at) {
            $updateData['approved_at'] = now();
        }

        $advertisement->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة الإعلان بنجاح'
        ]);
    }

    /**
     * حذف الإعلان
     */
    public function destroy(Advertisement $advertisement)
    {
        try {
            // حذف الصور والفيديوهات من التخزين
            foreach ($advertisement->images as $image) {
                Storage::disk('public')->delete($image->image_path);
            }

            foreach ($advertisement->videos as $video) {
                Storage::disk('public')->delete($video->video_path);
                if ($video->thumbnail_path) {
                    Storage::disk('public')->delete($video->thumbnail_path);
                }
            }

            $advertisement->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الإعلان بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الإعلان: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تبديل حالة الإعلان المميز
     */
    public function toggleFeatured(Advertisement $advertisement)
    {
        $advertisement->update([
            'is_featured' => !$advertisement->is_featured
        ]);

        return response()->json([
            'success' => true,
            'message' => $advertisement->is_featured ? 'تم تمييز الإعلان' : 'تم إلغاء تمييز الإعلان',
            'is_featured' => $advertisement->is_featured
        ]);
    }

    /**
     * حساب إحصائيات الإعلانات
     */
    private function getAdvertisementStats()
    {
        $totalAds = Advertisement::count();
        $pendingAds = Advertisement::where('status', 'pending')->count();
        $approvedAds = Advertisement::where('status', 'approved')->count();
        $rejectedAds = Advertisement::where('status', 'rejected')->count();
        $expiredAds = Advertisement::where('status', 'expired')->count();
        $featuredAds = Advertisement::where('is_featured', true)->count();

        // الإعلانات التي تنتهي خلال 7 أيام
        $expiringSoon = Advertisement::where('status', 'approved')
            ->where('expires_at', '<=', now()->addDays(7))
            ->where('expires_at', '>=', now())
            ->count();

        // إحصائيات اليوم
        $todayAds = Advertisement::whereDate('created_at', now()->toDateString())->count();
        $todayApproved = Advertisement::whereDate('approved_at', now()->toDateString())->count();

        return [
            'total_ads' => $totalAds,
            'pending_ads' => $pendingAds,
            'approved_ads' => $approvedAds,
            'rejected_ads' => $rejectedAds,
            'expired_ads' => $expiredAds,
            'featured_ads' => $featuredAds,
            'expiring_soon' => $expiringSoon,
            'today_ads' => $todayAds,
            'today_approved' => $todayApproved,
        ];
    }

    /**
     * جلب الإحصائيات عبر AJAX
     */
    public function getStats()
    {
        $stats = $this->getAdvertisementStats();

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * تحديد الإجراءات المتاحة للإعلان
     */
    private function getAdvertisementActions($advertisement)
    {
        $actions = [];

        // عرض التفاصيل
        $actions[] = [
            'type' => 'view',
            'label' => 'عرض التفاصيل',
            'class' => 'btn-info',
            'url' => route('admin.advertisements.show', $advertisement->id)
        ];

        // الموافقة
        if ($advertisement->status === 'pending') {
            $actions[] = [
                'type' => 'approve',
                'label' => 'موافقة',
                'class' => 'btn-success'
            ];
        }

        // الرفض
        if (in_array($advertisement->status, ['pending', 'approved'])) {
            $actions[] = [
                'type' => 'reject',
                'label' => 'رفض',
                'class' => 'btn-warning'
            ];
        }

        // تمييز/إلغاء تمييز
        $actions[] = [
            'type' => 'toggle_featured',
            'label' => $advertisement->is_featured ? 'إلغاء التمييز' : 'تمييز',
            'class' => $advertisement->is_featured ? 'btn-secondary' : 'btn-primary'
        ];

        // حذف
        $actions[] = [
            'type' => 'delete',
            'label' => 'حذف',
            'class' => 'btn-danger'
        ];

        return $actions;
    }
}
