<?php $__env->startSection('title', 'إدارة الإعلانات'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">الرئيسية</a></li>
    <li class="breadcrumb-item active">إدارة الإعلانات</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الإعلانات</h1>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الإعلانات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-ads">
                                <?php echo e($stats['total_ads']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-car fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-ads">
                                <?php echo e($stats['pending_ads']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                معتمدة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="approved-ads">
                                <?php echo e($stats['approved_ads']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                مميزة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="featured-ads">
                                <?php echo e($stats['featured_ads']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
        </div>
        <div class="card-body">
            <form id="filters-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="العنوان، الوصف، اسم المستخدم...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="pending">في الانتظار</option>
                                <option value="approved">معتمد</option>
                                <option value="rejected">مرفوض</option>
                                <option value="expired">منتهي الصلاحية</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="manufacturer_id">الشركة المصنعة</label>
                            <select class="form-control" id="manufacturer_id" name="manufacturer_id">
                                <option value="">الكل</option>
                                <?php $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($manufacturer->id); ?>"><?php echo e($manufacturer->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="governorate_id">المحافظة</label>
                            <select class="form-control" id="governorate_id" name="governorate_id">
                                <option value="">الكل</option>
                                <?php $__currentLoopData = $governorates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $governorate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($governorate->id); ?>"><?php echo e($governorate->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="user_id">المستخدم</label>
                            <select class="form-control" id="user_id" name="user_id">
                                <option value="">الكل</option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" id="apply-filters" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صف إضافي للفلاتر المتقدمة -->
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="price_from">السعر من</label>
                            <input type="number" class="form-control" id="price_from" name="price_from"
                                   placeholder="0" min="0">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="price_to">السعر إلى</label>
                            <input type="number" class="form-control" id="price_to" name="price_to"
                                   placeholder="1000000" min="0">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="year_from">السنة من</label>
                            <input type="number" class="form-control" id="year_from" name="year_from"
                                   placeholder="2000" min="1990" max="<?php echo e(date('Y') + 1); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="year_to">السنة إلى</label>
                            <input type="number" class="form-control" id="year_to" name="year_to"
                                   placeholder="<?php echo e(date('Y')); ?>" min="1990" max="<?php echo e(date('Y') + 1); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="per_page">عدد النتائج</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="15">15</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" id="clear-filters" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Advertisements Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                قائمة الإعلانات 
                <span id="ads-count" class="badge badge-primary">0</span>
            </h6>
            <div id="loading-indicator" class="d-none">
                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="ads-table" class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th style="width: 60px;">الصورة</th>
                            <th class="sortable" data-sort="title">
                                العنوان <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="user_id">
                                المستخدم <i class="fas fa-sort"></i>
                            </th>
                            <th>السيارة</th>
                            <th class="sortable" data-sort="status">
                                الحالة <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="price">
                                السعر <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="views_count">
                                المشاهدات <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="created_at">
                                تاريخ النشر <i class="fas fa-sort"></i>
                            </th>
                            <th style="width: 120px;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="ads-table-body">
                        <!-- سيتم تحميل البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="d-flex justify-content-center mt-3">
                <!-- سيتم تحميل الترقيم عبر AJAX -->
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/admin/ajax-table.js')); ?>"></script>
<script>
$(document).ready(function() {
    // إنشاء جدول الإعلانات التفاعلي
    const adsTable = new AjaxTable({
        tableId: '#ads-table',
        filtersFormId: '#filters-form',
        loadingIndicatorId: '#loading-indicator',
        paginationContainerId: '#pagination-container',
        counterId: '#ads-count',
        dataUrl: '<?php echo e(route("admin.advertisements.data")); ?>',
        renderRow: renderAdRow
    });

    // دالة عرض صف الإعلان
    function renderAdRow(ad) {
        const statusLabels = {
            'pending': '<span class="badge badge-warning">في الانتظار</span>',
            'approved': '<span class="badge badge-success">معتمد</span>',
            'rejected': '<span class="badge badge-danger">مرفوض</span>',
            'expired': '<span class="badge badge-secondary">منتهي الصلاحية</span>'
        };

        const createdAt = new Date(ad.created_at).toLocaleDateString('ar-SA');
        const carInfo = `${ad.manufacturer || ''} ${ad.model || ''}`.trim() || 'غير محدد';

        // صورة الإعلان
        const imageHtml = ad.main_image
            ? `<img src="${ad.main_image}" alt="${ad.title}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">`
            : '<div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border-radius: 4px;"><i class="fas fa-image text-muted"></i></div>';

        // إنشاء أزرار الإجراءات
        let actionsHtml = '';
        if (ad.actions && ad.actions.length > 0) {
            ad.actions.forEach(action => {
                if (action.type === 'view') {
                    actionsHtml += `<a class="dropdown-item" href="${action.url}">
                        <i class="fas fa-eye"></i> ${action.label}
                    </a>`;
                } else if (action.type === 'approve') {
                    actionsHtml += `<button class="dropdown-item text-success" onclick="updateStatus(${ad.id}, 'approved')">
                        <i class="fas fa-check"></i> ${action.label}
                    </button>`;
                } else if (action.type === 'reject') {
                    actionsHtml += `<button class="dropdown-item text-warning" onclick="showRejectModal(${ad.id})">
                        <i class="fas fa-times"></i> ${action.label}
                    </button>`;
                } else if (action.type === 'toggle_featured') {
                    actionsHtml += `<button class="dropdown-item" onclick="toggleFeatured(${ad.id})">
                        <i class="fas fa-star"></i> ${action.label}
                    </button>`;
                } else if (action.type === 'delete') {
                    actionsHtml += `<div class="dropdown-divider"></div>
                    <button class="dropdown-item text-danger" onclick="deleteAd(${ad.id})">
                        <i class="fas fa-trash"></i> ${action.label}
                    </button>`;
                }
            });
        }

        return `
            <tr>
                <td class="text-center">${imageHtml}</td>
                <td>
                    <div class="font-weight-bold">${ad.title}</div>
                    <div class="text-xs text-gray-600">${ad.description ? ad.description.substring(0, 40) + '...' : ''}</div>
                    ${ad.is_featured ? '<span class="badge badge-info badge-sm mt-1">مميز</span>' : ''}
                </td>
                <td>
                    <div class="font-weight-bold">${ad.user ? ad.user.name : '-'}</div>
                    <div class="text-xs text-gray-600">${ad.user ? ad.user.email : ''}</div>
                </td>
                <td>
                    <div>${carInfo}</div>
                    <div class="text-xs text-gray-600">${ad.year || ''} - ${ad.governorate || ''}</div>
                </td>
                <td>${statusLabels[ad.status] || ad.status}</td>
                <td class="font-weight-bold">${ad.formatted_price || 'غير محدد'}</td>
                <td>
                    <span class="badge badge-light">${ad.views_count || 0}</span>
                    ${ad.images_count > 0 ? `<small class="text-muted d-block">${ad.images_count} صورة</small>` : ''}
                </td>
                <td>${createdAt}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown">
                            إجراءات
                        </button>
                        <div class="dropdown-menu">
                            ${actionsHtml}
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    // دالة تحديث حالة الإعلان
    window.updateStatus = function(adId, status) {
        if (!confirm('هل أنت متأكد من تحديث حالة هذا الإعلان؟')) {
            return;
        }

        $.ajax({
            url: `/admin/advertisements/${adId}/status`,
            method: 'PATCH',
            data: { status: status },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    adsTable.refresh();
                    updateStats();
                    showAlert('success', response.message);
                }
            },
            error: function(xhr) {
                const errorMessage = xhr.responseJSON?.message || 'حدث خطأ أثناء تحديث الحالة';
                showAlert('error', errorMessage);
            }
        });
    };

    // دالة عرض نافذة الرفض
    window.showRejectModal = function(adId) {
        const reason = prompt('يرجى إدخال سبب رفض الإعلان:');
        if (reason && reason.trim()) {
            $.ajax({
                url: `/admin/advertisements/${adId}/status`,
                method: 'PATCH',
                data: {
                    status: 'rejected',
                    rejection_reason: reason.trim()
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.success) {
                        adsTable.refresh();
                        updateStats();
                        showAlert('success', response.message);
                    }
                },
                error: function(xhr) {
                    const errorMessage = xhr.responseJSON?.message || 'حدث خطأ أثناء رفض الإعلان';
                    showAlert('error', errorMessage);
                }
            });
        }
    };

    // دالة تبديل حالة التمييز
    window.toggleFeatured = function(adId) {
        $.ajax({
            url: `/admin/advertisements/${adId}/featured`,
            method: 'PATCH',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    adsTable.refresh();
                    updateStats();
                    showAlert('success', response.message);
                }
            },
            error: function(xhr) {
                showAlert('error', 'حدث خطأ أثناء تحديث حالة التمييز');
            }
        });
    };

    // دالة حذف الإعلان
    window.deleteAd = function(adId) {
        if (!confirm('هل أنت متأكد من حذف هذا الإعلان؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            return;
        }

        $.ajax({
            url: `/admin/advertisements/${adId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    adsTable.refresh();
                    updateStats();
                    showAlert('success', response.message);
                }
            },
            error: function(xhr) {
                showAlert('error', 'حدث خطأ أثناء حذف الإعلان');
            }
        });
    };

    // دالة تحديث الإحصائيات
    function updateStats() {
        $.ajax({
            url: '<?php echo e(route("admin.advertisements.stats")); ?>',
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success && response.stats) {
                    $('#total-ads').text(response.stats.total_ads);
                    $('#pending-ads').text(response.stats.pending_ads);
                    $('#approved-ads').text(response.stats.approved_ads);
                    $('#featured-ads').text(response.stats.featured_ads);
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحديث الإحصائيات:', xhr);
            }
        });
    }

    // دالة تحديث الإحصائيات
    function updateStats() {
        $.ajax({
            url: '<?php echo e(route("admin.advertisements.stats")); ?>',
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success && response.stats) {
                    $('#total-ads').text(response.stats.total_ads);
                    $('#pending-ads').text(response.stats.pending_ads);
                    $('#approved-ads').text(response.stats.approved_ads);
                    $('#featured-ads').text(response.stats.featured_ads);
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحديث الإحصائيات:', xhr);
            }
        });
    }

    // دالة عرض التنبيهات
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.content').prepend(alertHtml);

        // إخفاء التنبيه بعد 5 ثوان
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>

<style>
.sortable {
    cursor: pointer;
    user-select: none;
}

.sortable:hover {
    background-color: #f8f9fc;
}

#loading-indicator {
    color: #5a5c69;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/advertisements/index.blade.php ENDPATH**/ ?>