<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Advertisement extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'manufacturer_id',
        'model_id',
        'governorate_id',
        'district_id',
        'fuel_type_id',
        'transmission_type_id',
        'color_id',
        'title',
        'description',
        'price',
        'price_negotiable',
        'year',
        'mileage',
        'condition',
        'engine_size',
        'doors',
        'air_conditioning',
        'power_steering',
        'abs_brakes',
        'airbags',
        'sunroof',
        'leather_seats',
        'additional_features',
        'status',
        'rejection_reason',
        'is_featured',
        'views_count',
        'favorites_count',
        'approved_at',
        'expires_at',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'price_negotiable' => 'boolean',
        'air_conditioning' => 'boolean',
        'power_steering' => 'boolean',
        'abs_brakes' => 'boolean',
        'airbags' => 'boolean',
        'sunroof' => 'boolean',
        'leather_seats' => 'boolean',
        'is_featured' => 'boolean',
        'approved_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    // العلاقات
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function manufacturer()
    {
        return $this->belongsTo(Manufacturer::class);
    }

    public function model()
    {
        return $this->belongsTo(CarModel::class, 'model_id');
    }

    public function governorate()
    {
        return $this->belongsTo(Governorate::class);
    }

    public function district()
    {
        return $this->belongsTo(District::class);
    }

    public function fuelType()
    {
        return $this->belongsTo(FuelType::class);
    }

    public function transmissionType()
    {
        return $this->belongsTo(TransmissionType::class);
    }

    public function color()
    {
        return $this->belongsTo(Color::class);
    }

    public function images()
    {
        return $this->hasMany(AdvertisementImage::class);
    }

    public function videos()
    {
        return $this->hasMany(AdvertisementVideo::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    public function conversations()
    {
        return $this->hasMany(Conversation::class);
    }

    // دوال مساعدة
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    public function isPending()
    {
        return $this->status === 'pending';
    }

    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    public function isExpired()
    {
        return $this->expires_at && $this->expires_at < now();
    }

    public function incrementViews()
    {
        $this->increment('views_count');
    }

    public function getMainImage()
    {
        return $this->images()->where('is_primary', true)->first() 
               ?? $this->images()->orderBy('sort_order')->first();
    }

    public function getFormattedPrice()
    {
        $price = number_format($this->price, 0);
        return $price . ($this->price_negotiable ? ' (قابل للتفاوض)' : '') . ' ريال';
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeActive($query)
    {
        return $query->approved()->where(function($q) {
            $q->whereNull('expires_at')->orWhere('expires_at', '>', now());
        });
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
}
