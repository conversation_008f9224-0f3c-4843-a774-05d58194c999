@extends('admin.layouts.app')

@section('title', 'إضافة باقة جديدة')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إضافة باقة جديدة</h1>
            <p class="text-muted">إنشاء باقة اشتراك جديدة مع تحديد المميزات والأسعار</p>
        </div>
        <div>
            <a href="{{ route('admin.packages.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الباقة</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.packages.store') }}" method="POST">
                        @csrf
                        
                        <!-- اسم الباقة -->
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الباقة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- وصف الباقة -->
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الباقة</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">وصف مختصر لمميزات الباقة</div>
                        </div>

                        <div class="row">
                            <!-- السعر -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">السعر (ريال) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                           id="price" name="price" value="{{ old('price') }}" 
                                           min="0" step="0.01" required>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">ضع 0 للباقة المجانية</div>
                                </div>
                            </div>

                            <!-- مدة الباقة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="duration_days" class="form-label">مدة الباقة (بالأيام) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('duration_days') is-invalid @enderror" 
                                           id="duration_days" name="duration_days" value="{{ old('duration_days') }}" 
                                           min="1" required>
                                    @error('duration_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- عدد الإعلانات -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_ads" class="form-label">عدد الإعلانات المسموح <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('max_ads') is-invalid @enderror" 
                                           id="max_ads" name="max_ads" value="{{ old('max_ads') }}" 
                                           min="1" required>
                                    @error('max_ads')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- عدد الصور لكل إعلان -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_images_per_ad" class="form-label">عدد الصور لكل إعلان <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('max_images_per_ad') is-invalid @enderror" 
                                           id="max_images_per_ad" name="max_images_per_ad" value="{{ old('max_images_per_ad') }}" 
                                           min="1" max="20" required>
                                    @error('max_images_per_ad')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">الحد الأقصى 20 صورة</div>
                                </div>
                            </div>
                        </div>

                        <!-- المميزات -->
                        <div class="mb-3">
                            <label class="form-label">المميزات الإضافية</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="featured_ads" 
                                               name="featured_ads" value="1" {{ old('featured_ads') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="featured_ads">
                                            إعلانات مميزة
                                        </label>
                                        <div class="form-text">إظهار الإعلانات في المقدمة</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="video_allowed" 
                                               name="video_allowed" value="1" {{ old('video_allowed') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="video_allowed">
                                            السماح بالفيديو
                                        </label>
                                        <div class="form-text">إمكانية إضافة فيديو للإعلان</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- ترتيب العرض -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ترتيب العرض</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                                           min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                                </div>
                            </div>

                            <!-- حالة الباقة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة الباقة</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" 
                                               name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            باقة نشطة
                                        </label>
                                        <div class="form-text">الباقات غير النشطة لا تظهر للمستخدمين</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الباقة
                            </button>
                            <a href="{{ route('admin.packages.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معاينة الباقة -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معاينة الباقة</h5>
                </div>
                <div class="card-body">
                    <div id="package-preview" class="border rounded p-3">
                        <h6 id="preview-name" class="text-primary">اسم الباقة</h6>
                        <p id="preview-description" class="text-muted small">وصف الباقة</p>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">السعر:</span>
                            <span id="preview-price" class="text-success fw-bold">0 ريال</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>المدة:</span>
                            <span id="preview-duration">0 يوم</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>عدد الإعلانات:</span>
                            <span id="preview-ads">0</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الصور لكل إعلان:</span>
                            <span id="preview-images">0</span>
                        </div>
                        
                        <div id="preview-features" class="mt-3">
                            <small class="text-muted">المميزات الإضافية:</small>
                            <div id="features-list" class="mt-1">
                                <span class="text-muted">لا توجد مميزات إضافية</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نصائح -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-lightbulb text-warning me-2"></i>نصائح</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0 small">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>اختر اسماً واضحاً ومميزاً للباقة</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>حدد سعراً مناسباً للمميزات المقدمة</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>اكتب وصفاً مختصراً وواضحاً</li>
                        <li class="mb-0"><i class="fas fa-check text-success me-2"></i>استخدم ترتيب العرض لتنظيم الباقات</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث المعاينة عند تغيير القيم
    function updatePreview() {
        const name = $('#name').val() || 'اسم الباقة';
        const description = $('#description').val() || 'وصف الباقة';
        const price = parseFloat($('#price').val()) || 0;
        const duration = parseInt($('#duration_days').val()) || 0;
        const maxAds = parseInt($('#max_ads').val()) || 0;
        const maxImages = parseInt($('#max_images_per_ad').val()) || 0;
        const featuredAds = $('#featured_ads').is(':checked');
        const videoAllowed = $('#video_allowed').is(':checked');

        $('#preview-name').text(name);
        $('#preview-description').text(description);
        $('#preview-price').text(price > 0 ? price.toFixed(2) + ' ريال' : 'مجانية');
        $('#preview-duration').text(duration + ' يوم');
        $('#preview-ads').text(maxAds);
        $('#preview-images').text(maxImages);

        // المميزات
        const features = [];
        if (featuredAds) features.push('<span class="badge bg-warning text-dark me-1">مميزة</span>');
        if (videoAllowed) features.push('<span class="badge bg-info me-1">فيديو</span>');

        if (features.length > 0) {
            $('#features-list').html(features.join(''));
        } else {
            $('#features-list').html('<span class="text-muted">لا توجد مميزات إضافية</span>');
        }
    }

    // ربط الأحداث
    $('#name, #description, #price, #duration_days, #max_ads, #max_images_per_ad').on('input', updatePreview);
    $('#featured_ads, #video_allowed').on('change', updatePreview);

    // تحديث المعاينة عند تحميل الصفحة
    updatePreview();
});
</script>
@endpush
