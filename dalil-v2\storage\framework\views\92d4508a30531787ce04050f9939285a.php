<?php $__env->startSection('title', 'إدارة المستخدمين'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة المستخدمين</h1>
        <a href="<?php echo e(route('admin.users.create')); ?>" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> إضافة مستخدم جديد
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.users.index')); ?>">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="الاسم، البريد، الهاتف...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="user_type">نوع المستخدم</label>
                            <select class="form-control" id="user_type" name="user_type">
                                <option value="">الكل</option>
                                <option value="admin" <?php echo e(request('user_type') == 'admin' ? 'selected' : ''); ?>>مدير</option>
                                <option value="showroom" <?php echo e(request('user_type') == 'showroom' ? 'selected' : ''); ?>>معرض</option>
                                <option value="individual" <?php echo e(request('user_type') == 'individual' ? 'selected' : ''); ?>>فردي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>نشط</option>
                                <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>غير نشط</option>
                                <option value="suspended" <?php echo e(request('status') == 'suspended' ? 'selected' : ''); ?>>معلق</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="role">الدور</label>
                            <select class="form-control" id="role" name="role">
                                <option value="">الكل</option>
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($role->name); ?>" <?php echo e(request('role') == $role->name ? 'selected' : ''); ?>>
                                        <?php echo e($role->display_name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary mr-2">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة المستخدمين (<?php echo e($users->total()); ?> مستخدم)</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                            <th>الأدوار</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="icon-circle bg-primary">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="font-weight-bold"><?php echo e($user->name); ?></div>
                                            <?php if($user->company_name): ?>
                                                <div class="text-xs text-gray-600"><?php echo e($user->company_name); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo e($user->email); ?></td>
                                <td><?php echo e($user->phone ?? '-'); ?></td>
                                <td>
                                    <?php switch($user->user_type):
                                        case ('admin'): ?>
                                            <span class="badge badge-danger">مدير</span>
                                            <?php break; ?>
                                        <?php case ('showroom'): ?>
                                            <span class="badge badge-info">معرض</span>
                                            <?php break; ?>
                                        <?php case ('individual'): ?>
                                            <span class="badge badge-success">فردي</span>
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <?php switch($user->status):
                                        case ('active'): ?>
                                            <span class="badge badge-success">نشط</span>
                                            <?php break; ?>
                                        <?php case ('inactive'): ?>
                                            <span class="badge badge-secondary">غير نشط</span>
                                            <?php break; ?>
                                        <?php case ('suspended'): ?>
                                            <span class="badge badge-warning">معلق</span>
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge badge-primary badge-sm"><?php echo e($role->display_name); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </td>
                                <td><?php echo e($user->created_at->format('Y-m-d')); ?></td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                                data-toggle="dropdown">
                                            إجراءات
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="<?php echo e(route('admin.users.show', $user)); ?>">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a class="dropdown-item" href="<?php echo e(route('admin.users.edit', $user)); ?>">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <?php if($user->status !== 'suspended'): ?>
                                                <form method="POST" action="<?php echo e(route('admin.users.suspend', $user)); ?>" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('PATCH'); ?>
                                                    <button type="submit" class="dropdown-item text-warning"
                                                            onclick="return confirm('هل أنت متأكد من تعليق هذا المستخدم؟')">
                                                        <i class="fas fa-ban"></i> تعليق
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <form method="POST" action="<?php echo e(route('admin.users.toggle-status', $user)); ?>" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <button type="submit" class="dropdown-item <?php echo e($user->status === 'active' ? 'text-warning' : 'text-success'); ?>">
                                                    <i class="fas fa-<?php echo e($user->status === 'active' ? 'pause' : 'play'); ?>"></i>
                                                    <?php echo e($user->status === 'active' ? 'إلغاء التفعيل' : 'تفعيل'); ?>

                                                </button>
                                            </form>
                                            <div class="dropdown-divider"></div>
                                            <?php if($user->id !== auth()->id()): ?>
                                                <form method="POST" action="<?php echo e(route('admin.users.destroy', $user)); ?>" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="dropdown-item text-danger"
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-gray-500">
                                        <i class="fas fa-users fa-3x mb-3"></i>
                                        <p>لا توجد مستخدمين</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($users->hasPages()): ?>
                <div class="d-flex justify-content-center">
                    <?php echo e($users->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/users/index.blade.php ENDPATH**/ ?>