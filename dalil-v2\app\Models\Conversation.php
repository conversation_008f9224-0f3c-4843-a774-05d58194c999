<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Conversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'advertisement_id',
        'buyer_id',
        'seller_id',
        'last_message_at',
    ];

    protected $casts = [
        'last_message_at' => 'datetime',
    ];

    // العلاقات
    public function advertisement()
    {
        return $this->belongsTo(Advertisement::class);
    }

    public function buyer()
    {
        return $this->belongsTo(User::class, 'buyer_id');
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    // دوال مساعدة
    public function getOtherUser($currentUserId)
    {
        return $this->buyer_id == $currentUserId ? $this->seller : $this->buyer;
    }

    public function getLastMessage()
    {
        return $this->messages()->latest()->first();
    }

    public function hasUnreadMessages($userId)
    {
        return $this->messages()
                   ->where('sender_id', '!=', $userId)
                   ->where('is_read', false)
                   ->exists();
    }

    public function markAsRead($userId)
    {
        $this->messages()
             ->where('sender_id', '!=', $userId)
             ->where('is_read', false)
             ->update(['is_read' => true, 'read_at' => now()]);
    }

    public function updateLastMessageTime()
    {
        $this->update(['last_message_at' => now()]);
    }
}
