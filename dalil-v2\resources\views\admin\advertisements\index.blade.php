@extends('admin.layouts.app')

@section('title', 'إدارة الإعلانات')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
    <li class="breadcrumb-item active">إدارة الإعلانات</li>
@endsection

@section('content')
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الإعلانات</h1>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
        </div>
        <div class="card-body">
            <form id="filters-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="العنوان، الوصف، اسم المستخدم...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="pending">في الانتظار</option>
                                <option value="active">نشط</option>
                                <option value="rejected">مرفوض</option>
                                <option value="expired">منتهي الصلاحية</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="per_page">عدد النتائج</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="15">15</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" id="clear-filters" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Advertisements Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                قائمة الإعلانات 
                <span id="ads-count" class="badge badge-primary">0</span>
            </h6>
            <div id="loading-indicator" class="d-none">
                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="ads-table" class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="title">
                                العنوان <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="user_id">
                                المستخدم <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="status">
                                الحالة <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="price">
                                السعر <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="created_at">
                                تاريخ النشر <i class="fas fa-sort"></i>
                            </th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="ads-table-body">
                        <!-- سيتم تحميل البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="d-flex justify-content-center mt-3">
                <!-- سيتم تحميل الترقيم عبر AJAX -->
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="{{ asset('js/admin/ajax-table.js') }}"></script>
<script>
$(document).ready(function() {
    // إنشاء جدول الإعلانات التفاعلي
    const adsTable = new AjaxTable({
        tableId: '#ads-table',
        filtersFormId: '#filters-form',
        loadingIndicatorId: '#loading-indicator',
        paginationContainerId: '#pagination-container',
        counterId: '#ads-count',
        dataUrl: '{{ route("admin.advertisements.data") }}',
        renderRow: renderAdRow
    });

    // دالة عرض صف الإعلان
    function renderAdRow(ad) {
        const statusLabels = {
            'pending': '<span class="badge badge-warning">في الانتظار</span>',
            'active': '<span class="badge badge-success">نشط</span>',
            'rejected': '<span class="badge badge-danger">مرفوض</span>',
            'expired': '<span class="badge badge-secondary">منتهي الصلاحية</span>'
        };

        const createdAt = new Date(ad.created_at).toLocaleDateString('ar-SA');
        const price = ad.price ? new Intl.NumberFormat('ar-SA').format(ad.price) + ' ريال' : 'غير محدد';

        return `
            <tr>
                <td>
                    <div class="font-weight-bold">${ad.title}</div>
                    <div class="text-xs text-gray-600">${ad.description ? ad.description.substring(0, 50) + '...' : ''}</div>
                </td>
                <td>
                    <div class="font-weight-bold">${ad.user ? ad.user.name : '-'}</div>
                    <div class="text-xs text-gray-600">${ad.user ? ad.user.email : ''}</div>
                </td>
                <td>${statusLabels[ad.status] || ad.status}</td>
                <td>${price}</td>
                <td>${createdAt}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown">
                            إجراءات
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="/admin/advertisements/${ad.id}">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <div class="dropdown-divider"></div>
                            <button class="dropdown-item text-success" onclick="updateStatus(${ad.id}, 'active')">
                                <i class="fas fa-check"></i> موافقة
                            </button>
                            <button class="dropdown-item text-danger" onclick="updateStatus(${ad.id}, 'rejected')">
                                <i class="fas fa-times"></i> رفض
                            </button>
                            <div class="dropdown-divider"></div>
                            <button class="dropdown-item text-danger" onclick="deleteAd(${ad.id})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    // دالة تحديث حالة الإعلان
    window.updateStatus = function(adId, status) {
        if (!confirm('هل أنت متأكد من تحديث حالة هذا الإعلان؟')) {
            return;
        }

        $.ajax({
            url: `/admin/advertisements/${adId}/status`,
            method: 'PATCH',
            data: { status: status },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    adsTable.refresh();
                    // عرض رسالة نجاح
                    showAlert('success', response.message);
                }
            },
            error: function(xhr) {
                showAlert('error', 'حدث خطأ أثناء تحديث الحالة');
            }
        });
    };

    // دالة حذف الإعلان
    window.deleteAd = function(adId) {
        if (!confirm('هل أنت متأكد من حذف هذا الإعلان؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            return;
        }

        $.ajax({
            url: `/admin/advertisements/${adId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    adsTable.refresh();
                    showAlert('success', response.message);
                }
            },
            error: function(xhr) {
                showAlert('error', 'حدث خطأ أثناء حذف الإعلان');
            }
        });
    };

    // دالة عرض التنبيهات
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.content').prepend(alertHtml);
        
        // إخفاء التنبيه بعد 5 ثوان
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>

<style>
.sortable {
    cursor: pointer;
    user-select: none;
}

.sortable:hover {
    background-color: #f8f9fc;
}

#loading-indicator {
    color: #5a5c69;
}
</style>
@endpush
