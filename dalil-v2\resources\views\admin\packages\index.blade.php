@extends('admin.layouts.app')

@section('title', 'إدارة الباقات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة الباقات</h1>
            <p class="text-muted">إدارة باقات الاشتراك وتحديد الأسعار والمميزات</p>
        </div>
        <div>
            <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة باقة جديدة
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filters-form" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="البحث في اسم الباقة أو الوصف...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشطة</option>
                        <option value="inactive">غير نشطة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="price_range" class="form-label">نطاق السعر</label>
                    <select class="form-select" id="price_range" name="price_range">
                        <option value="">جميع الأسعار</option>
                        <option value="free">مجانية</option>
                        <option value="low">منخفضة (1-50)</option>
                        <option value="medium">متوسطة (51-200)</option>
                        <option value="high">عالية (+200)</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="per_page" class="form-label">عدد النتائج</label>
                    <select class="form-select" id="per_page" name="per_page">
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo me-1"></i>إعادة تعيين
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة الباقات</h5>
            <div id="data-count">إجمالي الباقات: 0</div>
        </div>
        <div class="card-body">
            <!-- Loading indicator -->
            <div id="loading-indicator" class="text-center py-3 d-none">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <div class="mt-2">جاري تحميل البيانات...</div>
            </div>

            <!-- Table -->
            <div class="table-responsive">
                <table class="table table-hover" id="packages-table">
                    <thead class="table-light">
                        <tr>
                            <th data-sort="id">#</th>
                            <th data-sort="name">اسم الباقة</th>
                            <th data-sort="price">السعر</th>
                            <th data-sort="duration_days">المدة</th>
                            <th data-sort="max_ads">عدد الإعلانات</th>
                            <th data-sort="max_images_per_ad">الصور</th>
                            <th>المميزات</th>
                            <th data-sort="subscriptions_count">الاشتراكات</th>
                            <th>الحالة</th>
                            <th data-sort="created_at">تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تحميل البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav id="pagination-container"></nav>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // إعداد جدول AJAX
    const packagesTable = new AjaxTable({
        tableId: '#packages-table',
        dataUrl: '{{ route("admin.packages.data") }}',
        renderRow: renderPackageRow,
        defaultSort: 'sort_order',
        defaultOrder: 'asc',
        filtersFormId: '#filters-form',
        loadingIndicatorId: '#loading-indicator',
        paginationContainerId: '#pagination-container',
        counterId: '#data-count'
    });

    // تطبيق الفلاتر
    window.applyFilters = function() {
        packagesTable.loadData();
    };

    // إعادة تعيين الفلاتر
    window.resetFilters = function() {
        $('#filters-form')[0].reset();
        packagesTable.loadData();
    };

    // حذف باقة
    window.deletePackage = function(id, name) {
        if (confirm(`هل أنت متأكد من حذف الباقة "${name}"؟\n\nملاحظة: لا يمكن حذف الباقة إذا كان لديها اشتراكات نشطة.`)) {
            $.ajax({
                url: `/admin/packages/${id}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        packagesTable.loadData();
                    } else {
                        showAlert('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('error', response?.message || 'حدث خطأ أثناء حذف الباقة');
                }
            });
        }
    };

    // تغيير حالة الباقة
    window.togglePackageStatus = function(id) {
        $.ajax({
            url: `/admin/packages/${id}/toggle-status`,
            method: 'PATCH',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    packagesTable.loadData();
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showAlert('error', response?.message || 'حدث خطأ أثناء تغيير حالة الباقة');
            }
        });
    };
});

// دالة عرض صف الباقة
function renderPackageRow(package) {
    const statusBadge = package.is_active 
        ? '<span class="badge bg-success">نشطة</span>'
        : '<span class="badge bg-secondary">غير نشطة</span>';

    const features = [];
    if (package.featured_ads) features.push('<span class="badge bg-warning text-dark">مميزة</span>');
    if (package.video_allowed) features.push('<span class="badge bg-info">فيديو</span>');
    
    const featuresHtml = features.length > 0 ? features.join(' ') : '<span class="text-muted">-</span>';

    const price = package.price > 0 
        ? `${parseFloat(package.price).toFixed(2)} ريال`
        : '<span class="badge bg-success">مجانية</span>';

    return `
        <tr>
            <td>${package.id}</td>
            <td>
                <div class="fw-bold">${package.name}</div>
                ${package.description ? `<small class="text-muted">${package.description.substring(0, 50)}${package.description.length > 50 ? '...' : ''}</small>` : ''}
            </td>
            <td>${price}</td>
            <td>${package.duration_days} يوم</td>
            <td>${package.max_ads}</td>
            <td>${package.max_images_per_ad}</td>
            <td>${featuresHtml}</td>
            <td>
                <span class="badge bg-primary">${package.subscriptions_count || 0}</span>
            </td>
            <td>${statusBadge}</td>
            <td>${new Date(package.created_at).toLocaleDateString('ar-SA')}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="/admin/packages/${package.id}" class="btn btn-outline-info" title="عرض">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="/admin/packages/${package.id}/edit" class="btn btn-outline-primary" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button type="button" class="btn btn-outline-${package.is_active ? 'warning' : 'success'}" 
                            onclick="togglePackageStatus(${package.id})" 
                            title="${package.is_active ? 'إلغاء التفعيل' : 'تفعيل'}">
                        <i class="fas fa-${package.is_active ? 'pause' : 'play'}"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" 
                            onclick="deletePackage(${package.id}, '${package.name}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// دالة عرض التنبيهات
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى الصفحة
    $('.container-fluid').prepend(alertHtml);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
@endpush
