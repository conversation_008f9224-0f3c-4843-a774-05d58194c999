<?php

namespace Database\Seeders;

use App\Models\Manufacturer;
use App\Models\CarModel;
use App\Models\Governorate;
use App\Models\District;
use App\Models\FuelType;
use App\Models\TransmissionType;
use App\Models\Color;
use Illuminate\Database\Seeder;

class BasicDataSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الشركات المصنعة
        $manufacturers = [
            ['name' => 'تويوتا', 'name_en' => 'Toyota', 'country' => 'اليابان', 'sort_order' => 1],
            ['name' => 'نيسان', 'name_en' => 'Nissan', 'country' => 'اليابان', 'sort_order' => 2],
            ['name' => 'هوندا', 'name_en' => 'Honda', 'country' => 'اليابان', 'sort_order' => 3],
            ['name' => 'مازda', 'name_en' => 'Mazda', 'country' => 'اليابان', 'sort_order' => 4],
            ['name' => 'ميتسوبيشي', 'name_en' => 'Mitsubishi', 'country' => 'اليابان', 'sort_order' => 5],
            ['name' => 'هيونداي', 'name_en' => 'Hyundai', 'country' => 'كوريا الجنوبية', 'sort_order' => 6],
            ['name' => 'كيا', 'name_en' => 'Kia', 'country' => 'كوريا الجنوبية', 'sort_order' => 7],
            ['name' => 'فولكس واجن', 'name_en' => 'Volkswagen', 'country' => 'ألمانيا', 'sort_order' => 8],
            ['name' => 'بي إم دبليو', 'name_en' => 'BMW', 'country' => 'ألمانيا', 'sort_order' => 9],
            ['name' => 'مرسيدس بنز', 'name_en' => 'Mercedes-Benz', 'country' => 'ألمانيا', 'sort_order' => 10],
            ['name' => 'أودي', 'name_en' => 'Audi', 'country' => 'ألمانيا', 'sort_order' => 11],
            ['name' => 'فورد', 'name_en' => 'Ford', 'country' => 'أمريكا', 'sort_order' => 12],
            ['name' => 'شيفروليه', 'name_en' => 'Chevrolet', 'country' => 'أمريكا', 'sort_order' => 13],
        ];

        foreach ($manufacturers as $manufacturer) {
            Manufacturer::create($manufacturer);
        }

        // إنشاء موديلات تويوتا
        $toyota = Manufacturer::where('name', 'تويوتا')->first();
        $toyotaModels = [
            ['name' => 'كامري', 'name_en' => 'Camry', 'year_from' => 1990, 'sort_order' => 1],
            ['name' => 'كورولا', 'name_en' => 'Corolla', 'year_from' => 1990, 'sort_order' => 2],
            ['name' => 'يارس', 'name_en' => 'Yaris', 'year_from' => 2000, 'sort_order' => 3],
            ['name' => 'برادو', 'name_en' => 'Prado', 'year_from' => 1995, 'sort_order' => 4],
            ['name' => 'هايلكس', 'name_en' => 'Hilux', 'year_from' => 1990, 'sort_order' => 5],
            ['name' => 'راف فور', 'name_en' => 'RAV4', 'year_from' => 1995, 'sort_order' => 6],
        ];

        foreach ($toyotaModels as $model) {
            CarModel::create(array_merge($model, ['manufacturer_id' => $toyota->id]));
        }

        // إنشاء موديلات نيسان
        $nissan = Manufacturer::where('name', 'نيسان')->first();
        $nissanModels = [
            ['name' => 'التيما', 'name_en' => 'Altima', 'year_from' => 1995, 'sort_order' => 1],
            ['name' => 'سنترا', 'name_en' => 'Sentra', 'year_from' => 1990, 'sort_order' => 2],
            ['name' => 'باترول', 'name_en' => 'Patrol', 'year_from' => 1990, 'sort_order' => 3],
            ['name' => 'إكس تريل', 'name_en' => 'X-Trail', 'year_from' => 2000, 'sort_order' => 4],
        ];

        foreach ($nissanModels as $model) {
            CarModel::create(array_merge($model, ['manufacturer_id' => $nissan->id]));
        }

        // إنشاء المحافظات اليمنية
        $governorates = [
            ['name' => 'أمانة العاصمة', 'name_en' => 'Amanat Al Asimah', 'sort_order' => 1],
            ['name' => 'عدن', 'name_en' => 'Aden', 'sort_order' => 2],
            ['name' => 'تعز', 'name_en' => 'Taiz', 'sort_order' => 3],
            ['name' => 'الحديدة', 'name_en' => 'Al Hudaydah', 'sort_order' => 4],
            ['name' => 'إب', 'name_en' => 'Ibb', 'sort_order' => 5],
            ['name' => 'ذمار', 'name_en' => 'Dhamar', 'sort_order' => 6],
            ['name' => 'حضرموت', 'name_en' => 'Hadramout', 'sort_order' => 7],
            ['name' => 'لحج', 'name_en' => 'Lahij', 'sort_order' => 8],
            ['name' => 'مأرب', 'name_en' => 'Marib', 'sort_order' => 9],
            ['name' => 'صعدة', 'name_en' => 'Saada', 'sort_order' => 10],
        ];

        foreach ($governorates as $governorate) {
            Governorate::create($governorate);
        }

        // إنشاء مديريات أمانة العاصمة
        $sanaa = Governorate::where('name', 'أمانة العاصمة')->first();
        $sanaaDistricts = [
            ['name' => 'معين', 'name_en' => 'Maeen', 'sort_order' => 1],
            ['name' => 'التحرير', 'name_en' => 'At Tahrir', 'sort_order' => 2],
            ['name' => 'الصافية', 'name_en' => 'As Safiyah', 'sort_order' => 3],
            ['name' => 'شعوب', 'name_en' => 'Shuaub', 'sort_order' => 4],
            ['name' => 'الثورة', 'name_en' => 'Ath Thawrah', 'sort_order' => 5],
        ];

        foreach ($sanaaDistricts as $district) {
            District::create(array_merge($district, ['governorate_id' => $sanaa->id]));
        }

        // إنشاء مديريات عدن
        $aden = Governorate::where('name', 'عدن')->first();
        $adenDistricts = [
            ['name' => 'كريتر', 'name_en' => 'Crater', 'sort_order' => 1],
            ['name' => 'المعلا', 'name_en' => 'Al Mualla', 'sort_order' => 2],
            ['name' => 'التواهي', 'name_en' => 'At Tawahi', 'sort_order' => 3],
            ['name' => 'خور مكسر', 'name_en' => 'Khor Maksar', 'sort_order' => 4],
            ['name' => 'الشيخ عثمان', 'name_en' => 'Ash Sheikh Othman', 'sort_order' => 5],
        ];

        foreach ($adenDistricts as $district) {
            District::create(array_merge($district, ['governorate_id' => $aden->id]));
        }

        // إنشاء أنواع الوقود
        $fuelTypes = [
            ['name' => 'بنزين', 'name_en' => 'Gasoline', 'sort_order' => 1],
            ['name' => 'ديزل', 'name_en' => 'Diesel', 'sort_order' => 2],
            ['name' => 'هجين', 'name_en' => 'Hybrid', 'sort_order' => 3],
            ['name' => 'كهربائي', 'name_en' => 'Electric', 'sort_order' => 4],
        ];

        foreach ($fuelTypes as $fuelType) {
            FuelType::create($fuelType);
        }

        // إنشاء أنواع ناقل الحركة
        $transmissionTypes = [
            ['name' => 'عادي', 'name_en' => 'Manual', 'sort_order' => 1],
            ['name' => 'أوتوماتيك', 'name_en' => 'Automatic', 'sort_order' => 2],
            ['name' => 'CVT', 'name_en' => 'CVT', 'sort_order' => 3],
        ];

        foreach ($transmissionTypes as $transmissionType) {
            TransmissionType::create($transmissionType);
        }

        // إنشاء الألوان
        $colors = [
            ['name' => 'أبيض', 'name_en' => 'White', 'hex_code' => '#FFFFFF', 'sort_order' => 1],
            ['name' => 'أسود', 'name_en' => 'Black', 'hex_code' => '#000000', 'sort_order' => 2],
            ['name' => 'فضي', 'name_en' => 'Silver', 'hex_code' => '#C0C0C0', 'sort_order' => 3],
            ['name' => 'رمادي', 'name_en' => 'Gray', 'hex_code' => '#808080', 'sort_order' => 4],
            ['name' => 'أحمر', 'name_en' => 'Red', 'hex_code' => '#FF0000', 'sort_order' => 5],
            ['name' => 'أزرق', 'name_en' => 'Blue', 'hex_code' => '#0000FF', 'sort_order' => 6],
            ['name' => 'أخضر', 'name_en' => 'Green', 'hex_code' => '#008000', 'sort_order' => 7],
            ['name' => 'بني', 'name_en' => 'Brown', 'hex_code' => '#A52A2A', 'sort_order' => 8],
            ['name' => 'ذهبي', 'name_en' => 'Gold', 'hex_code' => '#FFD700', 'sort_order' => 9],
        ];

        foreach ($colors as $color) {
            Color::create($color);
        }

        $this->command->info('تم إنشاء البيانات الأساسية بنجاح');
    }
}
