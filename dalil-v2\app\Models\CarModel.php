<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CarModel extends Model
{
    use HasFactory;

    protected $table = 'models';

    protected $fillable = [
        'manufacturer_id',
        'name',
        'name_en',
        'year_from',
        'year_to',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // العلاقات
    public function manufacturer()
    {
        return $this->belongsTo(Manufacturer::class);
    }

    public function advertisements()
    {
        return $this->hasMany(Advertisement::class, 'model_id');
    }

    // دوال مساعدة
    public function isActive()
    {
        return $this->is_active;
    }

    public static function getActive()
    {
        return static::where('is_active', true)->orderBy('sort_order')->get();
    }

    public function getYearRange()
    {
        if ($this->year_from && $this->year_to) {
            return $this->year_from . ' - ' . $this->year_to;
        } elseif ($this->year_from) {
            return 'من ' . $this->year_from;
        } elseif ($this->year_to) {
            return 'حتى ' . $this->year_to;
        }
        return 'جميع السنوات';
    }
}
