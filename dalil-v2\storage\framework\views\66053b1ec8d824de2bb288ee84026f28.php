<?php $__env->startSection('title', 'إدارة الاشتراكات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة الاشتراكات</h1>
            <p class="text-muted">إدارة اشتراكات المستخدمين في الباقات المختلفة</p>
        </div>
        <div>
            <a href="<?php echo e(route('admin.subscriptions.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة اشتراك جديد
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="total-subscriptions">0</h4>
                            <p class="mb-0">إجمالي الاشتراكات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="active-subscriptions">0</h4>
                            <p class="mb-0">اشتراكات نشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="expiring-subscriptions">0</h4>
                            <p class="mb-0">تنتهي قريباً</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="total-revenue">0</h4>
                            <p class="mb-0">إجمالي الإيرادات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>فلاتر البحث
                <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </h5>
        </div>
        <div class="collapse show" id="filtersCollapse">
            <div class="card-body">
                <form id="filters-form">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="اسم المستخدم أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="expired">منتهي</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="package_id" class="form-label">الباقة</label>
                            <select class="form-select" id="package_id" name="package_id">
                                <option value="all">جميع الباقات</option>
                                <?php $__currentLoopData = \App\Models\Package::orderBy('sort_order')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($package->id); ?>"><?php echo e($package->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="expiry_filter" class="form-label">تاريخ الانتهاء</label>
                            <select class="form-select" id="expiry_filter" name="expiry_filter">
                                <option value="">جميع التواريخ</option>
                                <option value="active">لم تنته بعد</option>
                                <option value="expiring_soon">تنتهي خلال أسبوع</option>
                                <option value="expired">منتهية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نطاق المبلغ</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="amount_min" name="amount_min" placeholder="من" min="0" step="0.01">
                                <span class="input-group-text">إلى</span>
                                <input type="number" class="form-control" id="amount_max" name="amount_max" placeholder="إلى" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary" id="apply-filters">
                                <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                                <i class="fas fa-times me-2"></i>مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- جدول الاشتراكات -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">قائمة الاشتراكات</h5>
        </div>
        <div class="card-body">
            <!-- مؤشر التحميل -->
            <div id="loading-indicator" class="text-center py-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
            </div>

            <!-- الجدول -->
            <div class="table-responsive">
                <table class="table table-hover" id="subscriptions-table">
                    <thead>
                        <tr>
                            <th data-sort="user">
                                المستخدم
                                <i class="fas fa-sort text-muted"></i>
                            </th>
                            <th data-sort="package">
                                الباقة
                                <i class="fas fa-sort text-muted"></i>
                            </th>
                            <th data-sort="start_date">
                                تاريخ البداية
                                <i class="fas fa-sort text-muted"></i>
                            </th>
                            <th data-sort="end_date">
                                تاريخ الانتهاء
                                <i class="fas fa-sort text-muted"></i>
                            </th>
                            <th data-sort="amount">
                                المبلغ
                                <i class="fas fa-sort text-muted"></i>
                            </th>
                            <th data-sort="status">
                                الحالة
                                <i class="fas fa-sort text-muted"></i>
                            </th>
                            <th>الأيام المتبقية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="subscriptions-tbody">
                        <!-- سيتم تحميل البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- التصفح -->
            <div id="pagination-container" class="d-flex justify-content-between align-items-center mt-3">
                <div id="data-info" class="text-muted">
                    <!-- معلومات البيانات -->
                </div>
                <nav id="pagination-nav">
                    <!-- أزرار التصفح -->
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal تجديد الاشتراك -->
<div class="modal fade" id="renewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تجديد الاشتراك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="renew-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="duration_days" class="form-label">مدة التجديد (بالأيام)</label>
                        <input type="number" class="form-control" id="duration_days" name="duration_days" min="1" max="365" required>
                    </div>
                    <div class="mb-3">
                        <label for="renew_amount" class="form-label">مبلغ التجديد</label>
                        <input type="number" class="form-control" id="renew_amount" name="amount" min="0" step="0.01" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">تجديد الاشتراك</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // إعداد جدول AJAX
    const subscriptionsTable = new AjaxTable({
        tableId: '#subscriptions-table',
        dataUrl: '<?php echo e(route("admin.subscriptions.data")); ?>',
        loadingIndicatorId: '#loading-indicator',
        paginationContainerId: '#pagination-container',
        dataInfoId: '#data-info',
        paginationNavId: '#pagination-nav',
        tbodyId: '#subscriptions-tbody',
        filtersFormId: '#filters-form',
        renderRow: renderSubscriptionRow,
        onDataLoaded: updateStats
    });

    // تطبيق الفلاتر
    $('#apply-filters').on('click', function() {
        subscriptionsTable.loadData();
    });

    // مسح الفلاتر
    $('#clear-filters').on('click', function() {
        $('#filters-form')[0].reset();
        subscriptionsTable.loadData();
    });

    // تحميل البيانات الأولية
    subscriptionsTable.loadData();

    // تجديد الاشتراك
    let renewSubscriptionId = null;
    
    $(document).on('click', '.renew-subscription', function(e) {
        e.preventDefault();
        renewSubscriptionId = $(this).data('id');
        $('#renewModal').modal('show');
    });

    $('#renew-form').on('submit', function(e) {
        e.preventDefault();
        
        if (!renewSubscriptionId) return;
        
        const formData = new FormData(this);
        
        $.ajax({
            url: `/admin/subscriptions/${renewSubscriptionId}/renew`,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#renewModal').modal('hide');
                    subscriptionsTable.loadData();
                    showAlert('success', response.message);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                showAlert('error', 'حدث خطأ في تجديد الاشتراك');
            }
        });
    });

    // إلغاء الاشتراك
    $(document).on('click', '.cancel-subscription', function(e) {
        e.preventDefault();
        
        if (!confirm('هل أنت متأكد من إلغاء هذا الاشتراك؟')) return;
        
        const subscriptionId = $(this).data('id');
        
        $.ajax({
            url: `/admin/subscriptions/${subscriptionId}/cancel`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    subscriptionsTable.loadData();
                    showAlert('success', response.message);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                showAlert('error', 'حدث خطأ في إلغاء الاشتراك');
            }
        });
    });

    // حذف الاشتراك
    $(document).on('click', '.delete-subscription', function(e) {
        e.preventDefault();
        
        if (!confirm('هل أنت متأكد من حذف هذا الاشتراك؟ هذا الإجراء لا يمكن التراجع عنه.')) return;
        
        const subscriptionId = $(this).data('id');
        
        $.ajax({
            url: `/admin/subscriptions/${subscriptionId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    subscriptionsTable.loadData();
                    showAlert('success', response.message);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function(xhr) {
                showAlert('error', 'حدث خطأ في حذف الاشتراك');
            }
        });
    });
});

// دالة عرض صف الاشتراك
function renderSubscriptionRow(subscription) {
    const daysRemaining = subscription.days_remaining;
    const daysRemainingClass = daysRemaining <= 7 ? 'text-warning' : 'text-success';
    
    let actionsHtml = '';
    subscription.actions.forEach(action => {
        if (action.type === 'renew') {
            actionsHtml += `<button class="btn btn-sm ${action.class} renew-subscription me-1" data-id="${subscription.id}" title="${action.label}">
                <i class="fas fa-redo"></i>
            </button>`;
        } else if (action.type === 'cancel') {
            actionsHtml += `<button class="btn btn-sm ${action.class} cancel-subscription me-1" data-id="${subscription.id}" title="${action.label}">
                <i class="fas fa-ban"></i>
            </button>`;
        } else if (action.type === 'delete') {
            actionsHtml += `<button class="btn btn-sm ${action.class} delete-subscription me-1" data-id="${subscription.id}" title="${action.label}">
                <i class="fas fa-trash"></i>
            </button>`;
        } else {
            actionsHtml += `<a href="${action.url}" class="btn btn-sm ${action.class} me-1" title="${action.label}">
                <i class="fas fa-${action.type === 'view' ? 'eye' : 'edit'}"></i>
            </a>`;
        }
    });

    return `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <div class="fw-bold">${subscription.user.name}</div>
                        <small class="text-muted">${subscription.user.email}</small>
                    </div>
                </div>
            </td>
            <td>
                <div class="fw-bold">${subscription.package.name}</div>
                <small class="text-muted">${subscription.package.price > 0 ? subscription.package.price + ' ريال' : 'مجانية'}</small>
            </td>
            <td>${subscription.start_date}</td>
            <td>
                ${subscription.end_date}
                ${subscription.is_expiring_soon ? '<span class="badge bg-warning text-dark ms-1">قريب الانتهاء</span>' : ''}
            </td>
            <td>${subscription.amount} ريال</td>
            <td>
                <span class="badge bg-${subscription.status_class}">${subscription.status_label}</span>
            </td>
            <td>
                <span class="${daysRemainingClass} fw-bold">${daysRemaining} يوم</span>
            </td>
            <td>
                ${actionsHtml}
            </td>
        </tr>
    `;
}

// تحديث الإحصائيات
function updateStats(data) {
    // يمكن إضافة منطق تحديث الإحصائيات هنا
    // أو استدعاء API منفصل للإحصائيات
}

// دالة عرض التنبيهات
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى الصفحة
    $('.container-fluid').prepend(alertHtml);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/subscriptions/index.blade.php ENDPATH**/ ?>