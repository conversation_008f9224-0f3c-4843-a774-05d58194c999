<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_id',
        'action',
        'notes',
    ];

    // العلاقات
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    // دوال مساعدة
    public function getActionLabel()
    {
        $labels = [
            'created' => 'تم إنشاء الاشتراك',
            'renewed' => 'تم تجديد الاشتراك',
            'cancelled' => 'تم إلغاء الاشتراك',
            'expired' => 'انتهت صلاحية الاشتراك',
        ];

        return $labels[$this->action] ?? $this->action;
    }

    public function getTimeAgo()
    {
        return $this->created_at->diffForHumans();
    }

    // Static methods
    public static function logAction($subscriptionId, $action, $notes = null)
    {
        return static::create([
            'subscription_id' => $subscriptionId,
            'action' => $action,
            'notes' => $notes,
        ]);
    }
}
