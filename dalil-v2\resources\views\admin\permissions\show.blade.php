@extends('admin.layouts.app')

@section('title', 'تفاصيل الصلاحية: ' . $permission->display_name)

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.permissions.index') }}">إدارة الصلاحيات</a></li>
    <li class="breadcrumb-item active">تفاصيل الصلاحية</li>
@endsection

@section('content')
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل الصلاحية: {{ $permission->display_name }}</h1>
        <div>
            <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn btn-primary btn-sm shadow-sm">
                <i class="fas fa-edit fa-sm text-white-50"></i> تعديل
            </a>
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary btn-sm shadow-sm">
                <i class="fas fa-arrow-right fa-sm text-white-50"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- معلومات الصلاحية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الصلاحية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">اسم الصلاحية:</label>
                                <p class="text-gray-800">{{ $permission->display_name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">الاسم التقني:</label>
                                <p class="text-gray-800"><code>{{ $permission->name }}</code></p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">المجموعة:</label>
                                <p class="text-gray-800">
                                    <span class="badge badge-info badge-lg">{{ $permission->group }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">عدد الأدوار المرتبطة:</label>
                                <p class="text-gray-800">
                                    <span class="badge badge-{{ $permission->roles->count() > 0 ? 'success' : 'secondary' }} badge-lg">
                                        {{ $permission->roles->count() }} دور
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($permission->description)
                        <div class="form-group">
                            <label class="font-weight-bold">الوصف:</label>
                            <p class="text-gray-800">{{ $permission->description }}</p>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">تاريخ الإنشاء:</label>
                                <p class="text-gray-800">{{ $permission->created_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">آخر تحديث:</label>
                                <p class="text-gray-800">{{ $permission->updated_at->format('Y-m-d H:i:s') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الأدوار المرتبطة بالصلاحية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">الأدوار المرتبطة بالصلاحية ({{ $permission->roles->count() }})</h6>
                </div>
                <div class="card-body">
                    @if($permission->roles->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>اسم الدور</th>
                                        <th>الاسم التقني</th>
                                        <th>عدد المستخدمين</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($permission->roles as $role)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-2">
                                                        <i class="fas fa-user-tag fa-2x text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <strong>{{ $role->display_name }}</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><code>{{ $role->name }}</code></td>
                                            <td>
                                                <span class="badge badge-{{ $role->users_count > 0 ? 'success' : 'secondary' }}">
                                                    {{ $role->users_count ?? $role->users()->count() }} مستخدم
                                                </span>
                                            </td>
                                            <td>{{ $role->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <a href="{{ route('admin.roles.show', $role) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                                <a href="{{ route('admin.roles.edit', $role) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا توجد أدوار مرتبطة بهذه الصلاحية حالياً.
                        </div>
                    @endif
                </div>
            </div>

            <!-- الصلاحيات ذات الصلة -->
            @if($relatedPermissions && $relatedPermissions->count() > 0)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">صلاحيات ذات صلة من نفس المجموعة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($relatedPermissions as $relatedPermission)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-left-info">
                                        <div class="card-body py-2">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-key text-info"></i>
                                                </div>
                                                <div>
                                                    <div class="font-weight-bold text-info">
                                                        {{ $relatedPermission->display_name }}
                                                    </div>
                                                    <div class="text-xs text-muted">
                                                        <code>{{ $relatedPermission->name }}</code>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <a href="{{ route('admin.permissions.show', $relatedPermission) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- إحصائيات الصلاحية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إحصائيات الصلاحية</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12">
                            <h3 class="text-primary">{{ $permission->roles->count() }}</h3>
                            <p class="text-muted mb-3">دور مرتبط</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h4 class="text-success">{{ $permission->roles->sum(function($role) { return $role->users()->count(); }) }}</h4>
                            <p class="text-muted mb-0">مستخدم يملك هذه الصلاحية</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل الصلاحية
                        </a>
                        
                        @if($permission->roles->count() == 0)
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash"></i> حذف الصلاحية
                            </button>
                        @else
                            <button type="button" class="btn btn-danger" disabled title="لا يمكن حذف الصلاحية لوجود أدوار مرتبطة بها">
                                <i class="fas fa-trash"></i> حذف الصلاحية
                            </button>
                        @endif

                        <a href="{{ route('admin.permissions.create') }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> إنشاء صلاحية جديدة
                        </a>

                        <a href="{{ route('admin.permissions.index', ['group' => $permission->group]) }}" class="btn btn-info">
                            <i class="fas fa-list"></i> عرض صلاحيات المجموعة
                        </a>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">معلومات إضافية</h6>
                </div>
                <div class="card-body">
                    <div class="text-muted">
                        <small>
                            <strong>معرف الصلاحية:</strong> {{ $permission->id }}<br>
                            <strong>الاسم التقني:</strong> <code>{{ $permission->name }}</code><br>
                            <strong>المجموعة:</strong> {{ $permission->group }}<br>
                            <strong>تاريخ الإنشاء:</strong> {{ $permission->created_at->diffForHumans() }}<br>
                            <strong>آخر تحديث:</strong> {{ $permission->updated_at->diffForHumans() }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد حذف الصلاحية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من رغبتك في حذف الصلاحية "<strong>{{ $permission->display_name }}</strong>"؟</p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form action="{{ route('admin.permissions.destroy', $permission) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">حذف الصلاحية</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function confirmDelete() {
    $('#deleteModal').modal('show');
}
</script>

<style>
.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-lg {
    font-size: 0.875em;
    padding: 8px 16px;
    border-radius: 20px;
}

.card .card-header h6 {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.875em;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.text-xs {
    font-size: 0.75rem;
}
</style>
@endpush
