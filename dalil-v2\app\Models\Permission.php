<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'module',
    ];

    // العلاقات
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_permissions');
    }

    // دوال مساعدة
    public static function getByModule($module)
    {
        return static::where('module', $module)->get();
    }
}
