@extends('admin.layouts.app')

@section('title', 'إضافة اشتراك جديد')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إضافة اشتراك جديد</h1>
            <p class="text-muted">إنشاء اشتراك جديد لمستخدم في باقة محددة</p>
        </div>
        <div>
            <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الاشتراك</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.subscriptions.store') }}" method="POST">
                        @csrf
                        
                        <!-- اختيار المستخدم -->
                        <div class="mb-3">
                            <label for="user_id" class="form-label">المستخدم <span class="text-danger">*</span></label>
                            <select class="form-select @error('user_id') is-invalid @enderror"
                                    id="user_id" name="user_id" required>
                                <option value="">اختر المستخدم</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }} ({{ $user->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">اختر المستخدم الذي سيحصل على الاشتراك</div>
                        </div>

                        <!-- اختيار الباقة -->
                        <div class="mb-3">
                            <label for="package_id" class="form-label">الباقة <span class="text-danger">*</span></label>
                            <select class="form-select @error('package_id') is-invalid @enderror" 
                                    id="package_id" name="package_id" required>
                                <option value="">اختر الباقة</option>
                                @foreach($packages as $package)
                                    <option value="{{ $package->id }}" 
                                            data-price="{{ $package->price }}"
                                            data-duration="{{ $package->duration_days }}"
                                            {{ old('package_id') == $package->id ? 'selected' : '' }}>
                                        {{ $package->name }} - {{ $package->getFormattedPrice() }} ({{ $package->duration_days }} يوم)
                                    </option>
                                @endforeach
                            </select>
                            @error('package_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">اختر الباقة المناسبة للاشتراك</div>
                        </div>

                        <div class="row">
                            <!-- تاريخ البداية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                           id="start_date" name="start_date" value="{{ old('start_date', date('Y-m-d')) }}" required>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- تاريخ الانتهاء (محسوب تلقائياً) -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date_display" class="form-label">تاريخ الانتهاء</label>
                                    <input type="text" class="form-control" id="end_date_display" readonly 
                                           placeholder="سيتم حسابه تلقائياً">
                                    <div class="form-text">يتم حساب تاريخ الانتهاء تلقائياً حسب مدة الباقة</div>
                                </div>
                            </div>
                        </div>

                        <!-- المبلغ المدفوع -->
                        <div class="mb-3">
                            <label for="amount_paid" class="form-label">المبلغ المدفوع (ريال) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('amount_paid') is-invalid @enderror"
                                   id="amount_paid" name="amount_paid" value="{{ old('amount_paid') }}"
                                   min="0" step="0.01" required>
                            @error('amount_paid')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">المبلغ المدفوع للاشتراك</div>
                        </div>

                        <!-- طريقة الدفع -->
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select @error('payment_method') is-invalid @enderror" 
                                    id="payment_method" name="payment_method">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>نقداً</option>
                                <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                                <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                <option value="mobile_payment" {{ old('payment_method') == 'mobile_payment' ? 'selected' : '' }}>دفع عبر الجوال</option>
                                <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>أخرى</option>
                            </select>
                            @error('payment_method')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الملاحظات -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">أي ملاحظات إضافية حول الاشتراك</div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>إنشاء الاشتراك
                            </button>
                            <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معاينة الاشتراك -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معاينة الاشتراك</h5>
                </div>
                <div class="card-body">
                    <div id="subscription-preview" class="border rounded p-3 bg-light">
                        <div id="preview-user" class="mb-3">
                            <h6 class="text-primary mb-1">المستخدم</h6>
                            <p class="text-muted mb-0" id="preview-user-name">لم يتم اختيار مستخدم</p>
                        </div>
                        
                        <div id="preview-package" class="mb-3">
                            <h6 class="text-primary mb-1">الباقة</h6>
                            <p class="text-muted mb-0" id="preview-package-name">لم يتم اختيار باقة</p>
                        </div>
                        
                        <div id="preview-dates" class="mb-3">
                            <h6 class="text-primary mb-1">المدة</h6>
                            <div class="d-flex justify-content-between">
                                <span>من:</span>
                                <span id="preview-start-date">--</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>إلى:</span>
                                <span id="preview-end-date">--</span>
                            </div>
                            <div class="d-flex justify-content-between fw-bold">
                                <span>المدة:</span>
                                <span id="preview-duration">-- يوم</span>
                            </div>
                        </div>
                        
                        <div id="preview-amount" class="mb-3">
                            <h6 class="text-primary mb-1">المبلغ</h6>
                            <p class="text-success fw-bold mb-0" id="preview-amount-value">0.00 ريال</p>
                        </div>
                        
                        <div id="preview-payment" class="mb-3">
                            <h6 class="text-primary mb-1">طريقة الدفع</h6>
                            <p class="text-muted mb-0" id="preview-payment-method">غير محدد</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تحذيرات -->
            <div class="card mt-3">
                <div class="card-header bg-warning">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات مهمة</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0 small">
                        <li>تأكد من أن المستخدم لا يملك اشتراك نشط آخر</li>
                        <li>تحقق من صحة المبلغ المدفوع</li>
                        <li>تاريخ الانتهاء يحسب تلقائياً حسب مدة الباقة</li>
                        <li>يمكن تعديل الاشتراك لاحقاً إذا لزم الأمر</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث المعاينة عند تغيير القيم
    function updatePreview() {
        // المستخدم
        const selectedUser = $('#user_id option:selected');
        if (selectedUser.val()) {
            $('#preview-user-name').text(selectedUser.text());
        } else {
            $('#preview-user-name').text('لم يتم اختيار مستخدم');
        }

        // الباقة
        const selectedPackage = $('#package_id option:selected');
        if (selectedPackage.val()) {
            $('#preview-package-name').text(selectedPackage.text());
            
            // تحديث المبلغ تلقائياً
            const packagePrice = selectedPackage.data('price');
            if (packagePrice !== undefined && !$('#amount_paid').val()) {
                $('#amount_paid').val(packagePrice);
            }
        } else {
            $('#preview-package-name').text('لم يتم اختيار باقة');
        }

        // التواريخ
        const startDate = $('#start_date').val();
        const selectedPackageDuration = selectedPackage.data('duration');
        
        $('#preview-start-date').text(startDate || '--');
        
        if (startDate && selectedPackageDuration) {
            const start = new Date(startDate);
            const end = new Date(start);
            end.setDate(start.getDate() + parseInt(selectedPackageDuration));
            
            const endDateString = end.toISOString().split('T')[0];
            $('#preview-end-date').text(endDateString);
            $('#end_date_display').val(endDateString);
            $('#preview-duration').text(selectedPackageDuration + ' يوم');
        } else {
            $('#preview-end-date').text('--');
            $('#end_date_display').val('');
            $('#preview-duration').text('-- يوم');
        }

        // المبلغ
        const amount = parseFloat($('#amount_paid').val()) || 0;
        $('#preview-amount-value').text(amount.toFixed(2) + ' ريال');

        // طريقة الدفع
        const paymentMethod = $('#payment_method option:selected').text();
        if ($('#payment_method').val()) {
            $('#preview-payment-method').text(paymentMethod);
        } else {
            $('#preview-payment-method').text('غير محدد');
        }
    }

    // ربط الأحداث
    $('#user_id, #package_id, #start_date, #amount_paid, #payment_method').on('change input', updatePreview);

    // تحديث المعاينة عند تحميل الصفحة
    updatePreview();
});
</script>
@endpush
