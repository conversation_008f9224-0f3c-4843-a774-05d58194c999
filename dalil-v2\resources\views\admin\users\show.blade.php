@extends('admin.layouts.app')

@section('title', 'تفاصيل المستخدم - ' . $user->name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل المستخدم</h1>
        <div>
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-sm btn-primary shadow-sm mr-2">
                <i class="fas fa-edit fa-sm text-white-50"></i> تعديل
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">المعلومات الشخصية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">الاسم:</td>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">البريد الإلكتروني:</td>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">رقم الهاتف:</td>
                                    <td>{{ $user->phone ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">نوع المستخدم:</td>
                                    <td>
                                        @switch($user->user_type)
                                            @case('admin')
                                                <span class="badge badge-danger">مدير</span>
                                                @break
                                            @case('showroom')
                                                <span class="badge badge-info">معرض</span>
                                                @break
                                            @case('individual')
                                                <span class="badge badge-success">فردي</span>
                                                @break
                                        @endswitch
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">الحالة:</td>
                                    <td>
                                        @switch($user->status)
                                            @case('active')
                                                <span class="badge badge-success">نشط</span>
                                                @break
                                            @case('inactive')
                                                <span class="badge badge-secondary">غير نشط</span>
                                                @break
                                            @case('suspended')
                                                <span class="badge badge-warning">معلق</span>
                                                @break
                                        @endswitch
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">تاريخ التسجيل:</td>
                                    <td>{{ $user->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">آخر تحديث:</td>
                                    <td>{{ $user->updated_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                @if($user->company_name)
                                <tr>
                                    <td class="font-weight-bold">اسم الشركة:</td>
                                    <td>{{ $user->company_name }}</td>
                                </tr>
                                @endif
                                @if($user->commercial_register)
                                <tr>
                                    <td class="font-weight-bold">السجل التجاري:</td>
                                    <td>{{ $user->commercial_register }}</td>
                                </tr>
                                @endif
                                @if($user->address)
                                <tr>
                                    <td class="font-weight-bold">العنوان:</td>
                                    <td>{{ $user->address }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الأدوار والصلاحيات</h6>
                </div>
                <div class="card-body">
                    @if($user->roles->count() > 0)
                        <div class="row">
                            @foreach($user->roles as $role)
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-primary">
                                        <div class="card-body py-3">
                                            <div class="d-flex align-items-center">
                                                <div class="mr-3">
                                                    <i class="fas fa-user-shield text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="font-weight-bold">{{ $role->display_name }}</div>
                                                    @if($role->description)
                                                        <div class="text-xs text-gray-600">{{ $role->description }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center text-gray-500">
                            <i class="fas fa-user-shield fa-3x mb-3"></i>
                            <p>لا توجد أدوار مخصصة لهذا المستخدم</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="col-lg-4">
            <!-- Stats Cards -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإحصائيات</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="card border-left-info">
                                <div class="card-body py-3">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        إجمالي الإعلانات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_ads'] }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card border-left-success">
                                <div class="card-body py-3">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        إعلانات نشطة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active_ads'] }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card border-left-warning">
                                <div class="card-body py-3">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        إعلانات معلقة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_ads'] }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body py-3">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        الاشتراكات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_subscriptions'] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($user->status !== 'suspended')
                            <form method="POST" action="{{ route('admin.users.suspend', $user) }}">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-warning btn-block"
                                        onclick="return confirm('هل أنت متأكد من تعليق هذا المستخدم؟')">
                                    <i class="fas fa-ban"></i> تعليق المستخدم
                                </button>
                            </form>
                        @endif
                        
                        <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-{{ $user->status === 'active' ? 'secondary' : 'success' }} btn-block">
                                <i class="fas fa-{{ $user->status === 'active' ? 'pause' : 'play' }}"></i>
                                {{ $user->status === 'active' ? 'إلغاء التفعيل' : 'تفعيل' }}
                            </button>
                        </form>

                        @if($user->id !== auth()->id())
                            <form method="POST" action="{{ route('admin.users.destroy', $user) }}">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-block"
                                        onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <i class="fas fa-trash"></i> حذف المستخدم
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    @if($user->subscriptions->count() > 0)
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الاشتراكات الحديثة</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>الباقة</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($user->subscriptions->take(5) as $subscription)
                                    <tr>
                                        <td>{{ $subscription->package->name ?? '-' }}</td>
                                        <td>{{ number_format($subscription->amount_paid) }} ريال</td>
                                        <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                        <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                        <td>
                                            @switch($subscription->status)
                                                @case('active')
                                                    <span class="badge badge-success">نشط</span>
                                                    @break
                                                @case('expired')
                                                    <span class="badge badge-danger">منتهي</span>
                                                    @break
                                                @case('cancelled')
                                                    <span class="badge badge-secondary">ملغي</span>
                                                    @break
                                            @endswitch
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
