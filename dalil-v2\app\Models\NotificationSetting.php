<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'email_messages',
        'email_ad_updates',
        'email_subscription_alerts',
        'sms_messages',
        'sms_ad_updates',
        'sms_subscription_alerts',
    ];

    protected $casts = [
        'email_messages' => 'boolean',
        'email_ad_updates' => 'boolean',
        'email_subscription_alerts' => 'boolean',
        'sms_messages' => 'boolean',
        'sms_ad_updates' => 'boolean',
        'sms_subscription_alerts' => 'boolean',
    ];

    // العلاقات
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // دوال مساعدة
    public function shouldSendEmailFor($type)
    {
        switch ($type) {
            case 'message':
                return $this->email_messages;
            case 'ad_update':
                return $this->email_ad_updates;
            case 'subscription_alert':
                return $this->email_subscription_alerts;
            default:
                return false;
        }
    }

    public function shouldSendSmsFor($type)
    {
        switch ($type) {
            case 'message':
                return $this->sms_messages;
            case 'ad_update':
                return $this->sms_ad_updates;
            case 'subscription_alert':
                return $this->sms_subscription_alerts;
            default:
                return false;
        }
    }

    // Static methods
    public static function createDefaultForUser($userId)
    {
        return static::create([
            'user_id' => $userId,
            'email_messages' => true,
            'email_ad_updates' => true,
            'email_subscription_alerts' => true,
            'sms_messages' => false,
            'sms_ad_updates' => false,
            'sms_subscription_alerts' => true,
        ]);
    }
}
