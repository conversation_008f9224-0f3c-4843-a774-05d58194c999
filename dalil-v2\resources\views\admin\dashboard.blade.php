@extends('admin.layouts.app')

@section('title', 'لوحة التحكم الرئيسية')

@section('breadcrumb')
    <li class="breadcrumb-item active">لوحة التحكم</li>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">لوحة التحكم الرئيسية</h1>
            <p class="text-muted">مرحباً {{ auth()->user()->name }}، إليك نظرة عامة على النظام</p>
        </div>
        <div>
            <span class="badge bg-success fs-6">
                <i class="fas fa-circle me-1"></i>
                النظام يعمل بشكل طبيعي
            </span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إجمالي المستخدمين
                            </div>
                            <div class="h5 mb-0 font-weight-bold">
                                {{ number_format($stats['total_users']) }}
                            </div>
                            <div class="mt-2 text-xs">
                                <span class="text-success">
                                    <i class="fas fa-user-check"></i>
                                    {{ $stats['active_users'] }} نشط
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card success h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إجمالي الإعلانات
                            </div>
                            <div class="h5 mb-0 font-weight-bold">
                                {{ number_format($stats['total_ads']) }}
                            </div>
                            <div class="mt-2 text-xs">
                                <span class="text-warning">
                                    <i class="fas fa-clock"></i>
                                    {{ $stats['pending_ads'] }} في الانتظار
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-car fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card warning h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                الاشتراكات النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold">
                                {{ number_format($stats['active_subscriptions']) }}
                            </div>
                            <div class="mt-2 text-xs">
                                من أصل {{ $stats['total_subscriptions'] }} اشتراك
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card info h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إجمالي الإيرادات
                            </div>
                            <div class="h5 mb-0 font-weight-bold">
                                {{ number_format($stats['total_revenue']) }} ر.ي
                            </div>
                            <div class="mt-2 text-xs">
                                من الاشتراكات النشطة
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Users by Type Chart -->
        <div class="col-xl-4 col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">المستخدمين حسب النوع</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="userTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ads by Status Chart -->
        <div class="col-xl-4 col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الإعلانات حسب الحالة</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="adStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Subscriptions Chart -->
        <div class="col-xl-4 col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الاشتراكات الشهرية</h6>
                </div>
                <div class="card-body">
                    <div class="chart-bar pt-4 pb-2">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Row -->
    <div class="row">
        <!-- Recent Users -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">أحدث المستخدمين</h6>
                    <a href="#" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentUsers as $user)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar me-3">
                                                <i class="fas fa-user-circle fa-2x text-muted"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ $user->name }}</div>
                                                <div class="text-muted small">{{ $user->email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ $user->user_type == 'admin' ? 'إداري' : ($user->user_type == 'showroom' ? 'معرض' : 'فردي') }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $user->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                            {{ $user->status == 'active' ? 'نشط' : 'معطل' }}
                                        </span>
                                    </td>
                                    <td>{{ $user->created_at->format('Y-m-d') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center text-muted">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Ads -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الإعلانات المعلقة</h6>
                    <a href="#" class="btn btn-sm btn-warning">مراجعة الكل</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>المعلن</th>
                                    <th>السعر</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pendingAds as $ad)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $ad->title }}</div>
                                        <div class="text-muted small">
                                            {{ $ad->manufacturer->name ?? 'غير محدد' }}
                                            {{ $ad->carModel->name ?? '' }}
                                        </div>
                                    </td>
                                    <td>{{ $ad->user->name }}</td>
                                    <td class="fw-bold text-success">{{ number_format($ad->price) }} ر.ي</td>
                                    <td>{{ $ad->created_at->format('Y-m-d') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center text-muted">لا توجد إعلانات معلقة</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// User Type Chart
const userTypeCtx = document.getElementById('userTypeChart').getContext('2d');
const userTypeChart = new Chart(userTypeCtx, {
    type: 'doughnut',
    data: {
        labels: ['إداري', 'معرض', 'فردي'],
        datasets: [{
            data: [
                {{ $usersByType['admin'] ?? 0 }},
                {{ $usersByType['showroom'] ?? 0 }},
                {{ $usersByType['individual'] ?? 0 }}
            ],
            backgroundColor: ['#667eea', '#764ba2', '#f093fb'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Ad Status Chart
const adStatusCtx = document.getElementById('adStatusChart').getContext('2d');
const adStatusChart = new Chart(adStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['نشط', 'معلق', 'مرفوض', 'منتهي'],
        datasets: [{
            data: [
                {{ $adsByStatus['active'] ?? 0 }},
                {{ $adsByStatus['pending'] ?? 0 }},
                {{ $adsByStatus['rejected'] ?? 0 }},
                {{ $adsByStatus['expired'] ?? 0 }}
            ],
            backgroundColor: ['#11998e', '#f5576c', '#ff6b6b', '#95a5a6'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Monthly Subscriptions Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
        datasets: [{
            label: 'عدد الاشتراكات',
            data: [
                @php
                    $monthlyData = array_fill(1, 12, 0);
                    foreach($monthlySubscriptions as $sub) {
                        $monthlyData[intval($sub->month)] = $sub->count;
                    }
                    echo implode(',', array_values($monthlyData));
                @endphp
            ],
            backgroundColor: 'rgba(102, 126, 234, 0.8)',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
</script>
@endpush
