<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // التحقق من تسجيل الدخول
        if (!Auth::check()) {
            return redirect()->route('admin.login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $user = Auth::user();

        // التحقق من أن المستخدم من النوع الإداري
        if ($user->user_type !== 'admin') {
            Auth::logout();
            return redirect()->route('admin.login')->with('error', 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        // التحقق من حالة المستخدم
        if ($user->status !== 'active') {
            Auth::logout();
            return redirect()->route('admin.login')->with('error', 'حسابك معطل، يرجى التواصل مع الإدارة');
        }

        // التحقق من وجود أدوار للمستخدم (المدير العام معفى من هذا الشرط)
        if (!$user->isSuperAdmin() && !$user->roles()->exists()) {
            Auth::logout();
            return redirect()->route('admin.login')->with('error', 'لا توجد صلاحيات مخصصة لحسابك');
        }

        return $next($request);
    }
}
