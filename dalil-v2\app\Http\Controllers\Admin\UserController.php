<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{
    /**
     * عرض قائمة المستخدمين
     */
    public function index(Request $request)
    {
        $roles = Role::all();

        // إذا كان الطلب AJAX، إرجاع البيانات فقط
        if ($request->ajax()) {
            return $this->getUsersData($request);
        }

        return view('admin.users.index', compact('roles'));
    }

    /**
     * جلب بيانات المستخدمين عبر AJAX
     */
    public function getUsersData(Request $request)
    {
        $query = User::with(['roles', 'subscriptions']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->filled('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب الدور
        if ($request->filled('role')) {
            $query->whereHas('roles', function($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDir = $request->get('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        $users = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $users->items(),
            'pagination' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
                'from' => $users->firstItem(),
                'to' => $users->lastItem(),
            ],
            'links' => $users->appends(request()->query())->links()->render()
        ]);
    }

    /**
     * عرض نموذج إنشاء مستخدم جديد
     */
    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * حفظ مستخدم جديد
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'user_type' => 'required|in:admin,showroom,individual',
            'status' => 'required|in:active,inactive,suspended',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
            'company_name' => 'nullable|string|max:255',
            'commercial_register' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'governorate_id' => 'nullable|exists:governorates,id',
            'district_id' => 'nullable|exists:districts,id',
        ], [
            'name.required' => 'الاسم مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم مسبقاً',
            'phone.unique' => 'رقم الهاتف مستخدم مسبقاً',
            'password.required' => 'كلمة المرور مطلوبة',
            'password.min' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق',
            'user_type.required' => 'نوع المستخدم مطلوب',
            'status.required' => 'حالة المستخدم مطلوبة',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'user_type' => $request->user_type,
                'status' => $request->status,
                'company_name' => $request->company_name,
                'commercial_register' => $request->commercial_register,
                'address' => $request->address,
                'governorate_id' => $request->governorate_id,
                'district_id' => $request->district_id,
            ]);

            // ربط الأدوار
            if ($request->filled('roles')) {
                $user->roles()->sync($request->roles);
            }

            DB::commit();

            return redirect()->route('admin.users.index')
                ->with('success', 'تم إنشاء المستخدم بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء المستخدم')
                ->withInput();
        }
    }

    /**
     * عرض تفاصيل مستخدم محدد
     */
    public function show(User $user)
    {
        $user->load(['roles', 'subscriptions.package', 'advertisements', 'governorate', 'district']);

        // إحصائيات المستخدم
        $stats = [
            'total_ads' => $user->advertisements()->count(),
            'active_ads' => $user->advertisements()->where('status', 'active')->count(),
            'pending_ads' => $user->advertisements()->where('status', 'pending')->count(),
            'total_subscriptions' => $user->subscriptions()->count(),
            'active_subscriptions' => $user->subscriptions()->where('status', 'active')->count(),
        ];

        return view('admin.users.show', compact('user', 'stats'));
    }

    /**
     * عرض نموذج تعديل مستخدم
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * تحديث بيانات مستخدم
     */
    public function update(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20|unique:users,phone,' . $user->id,
            'password' => 'nullable|string|min:6|confirmed',
            'user_type' => 'required|in:admin,showroom,individual',
            'status' => 'required|in:active,inactive,suspended',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
            'company_name' => 'nullable|string|max:255',
            'commercial_register' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'governorate_id' => 'nullable|exists:governorates,id',
            'district_id' => 'nullable|exists:districts,id',
        ], [
            'name.required' => 'الاسم مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم مسبقاً',
            'phone.unique' => 'رقم الهاتف مستخدم مسبقاً',
            'password.min' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق',
            'user_type.required' => 'نوع المستخدم مطلوب',
            'status.required' => 'حالة المستخدم مطلوبة',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'user_type' => $request->user_type,
                'status' => $request->status,
                'company_name' => $request->company_name,
                'commercial_register' => $request->commercial_register,
                'address' => $request->address,
                'governorate_id' => $request->governorate_id,
                'district_id' => $request->district_id,
            ];

            // تحديث كلمة المرور إذا تم إدخالها
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            // ربط الأدوار
            if ($request->has('roles')) {
                $user->roles()->sync($request->roles ?? []);
            }

            DB::commit();

            return redirect()->route('admin.users.index')
                ->with('success', 'تم تحديث المستخدم بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث المستخدم')
                ->withInput();
        }
    }

    /**
     * حذف مستخدم
     */
    public function destroy(User $user)
    {
        try {
            // التحقق من عدم حذف المدير الحالي
            if ($user->id === auth()->id()) {
                return redirect()->back()
                    ->with('error', 'لا يمكنك حذف حسابك الخاص');
            }

            // التحقق من وجود اشتراكات نشطة
            if ($user->subscriptions()->where('status', 'active')->exists()) {
                return redirect()->back()
                    ->with('error', 'لا يمكن حذف المستخدم لوجود اشتراكات نشطة');
            }

            DB::beginTransaction();

            // حذف الأدوار المرتبطة
            $user->roles()->detach();

            // حذف المستخدم
            $user->delete();

            DB::commit();

            return redirect()->route('admin.users.index')
                ->with('success', 'تم حذف المستخدم بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف المستخدم');
        }
    }

    /**
     * تغيير حالة المستخدم
     */
    public function toggleStatus(User $user)
    {
        try {
            $newStatus = $user->status === 'active' ? 'inactive' : 'active';
            $user->update(['status' => $newStatus]);

            $message = $newStatus === 'active' ? 'تم تفعيل المستخدم' : 'تم إلغاء تفعيل المستخدم';

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء تغيير حالة المستخدم');
        }
    }

    /**
     * تعليق المستخدم
     */
    public function suspend(User $user)
    {
        try {
            $user->update(['status' => 'suspended']);
            return redirect()->back()->with('success', 'تم تعليق المستخدم بنجاح');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء تعليق المستخدم');
        }
    }
}
