<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'user_type',
        'status',
        'avatar',
        'bio',
        'city',
        'address',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // العلاقات
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_roles');
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function advertisements()
    {
        return $this->hasMany(Advertisement::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    public function conversations()
    {
        return $this->hasMany(Conversation::class, 'buyer_id')
                    ->orWhere('seller_id', $this->id);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function notificationSettings()
    {
        return $this->hasOne(NotificationSetting::class);
    }

    // دوال مساعدة
    public function hasRole($role)
    {
        return $this->roles()->where('name', $role)->exists();
    }

    public function hasPermission($permission)
    {
        // المدير العام له جميع الصلاحيات
        if ($this->email === '<EMAIL>') {
            return true;
        }

        return $this->roles()->whereHas('permissions', function ($query) use ($permission) {
            $query->where('name', $permission);
        })->exists();
    }

    public function isAdmin()
    {
        return $this->user_type === 'admin';
    }

    public function isShowroom()
    {
        return $this->user_type === 'showroom';
    }

    public function isIndividual()
    {
        return $this->user_type === 'individual';
    }

    public function isActive()
    {
        return $this->status === 'active';
    }

    public function isSuperAdmin()
    {
        return $this->email === '<EMAIL>' || $this->hasRole('super_admin');
    }
}
