<?php $__env->startSection('title', 'تعديل الاشتراك'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تعديل الاشتراك</h1>
            <p class="text-muted">تعديل بيانات اشتراك <?php echo e($subscription->user->name); ?></p>
        </div>
        <div>
            <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
            <a href="<?php echo e(route('admin.subscriptions.show', $subscription)); ?>" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i>عرض التفاصيل
            </a>
        </div>
    </div>

    <!-- تحذيرات -->
    <?php if($subscription->status === 'active'): ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> هذا الاشتراك نشط حالياً. كن حذراً عند تعديل التواريخ أو المبلغ.
        </div>
    <?php endif; ?>

    <?php if($subscription->status === 'expired'): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>معلومة:</strong> هذا الاشتراك منتهي الصلاحية. يمكنك تجديده أو تعديل تواريخه.
        </div>
    <?php endif; ?>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الاشتراك</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.subscriptions.update', $subscription)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- معلومات المستخدم (للعرض فقط) -->
                        <div class="mb-3">
                            <label class="form-label">المستخدم</label>
                            <div class="form-control-plaintext border rounded p-2 bg-light">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo e($subscription->user->name); ?></div>
                                        <small class="text-muted"><?php echo e($subscription->user->email); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="form-text">لا يمكن تغيير المستخدم بعد إنشاء الاشتراك</div>
                        </div>

                        <!-- اختيار الباقة -->
                        <div class="mb-3">
                            <label for="package_id" class="form-label">الباقة <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['package_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="package_id" name="package_id" required>
                                <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($package->id); ?>" 
                                            data-price="<?php echo e($package->price); ?>"
                                            data-duration="<?php echo e($package->duration_days); ?>"
                                            <?php echo e(old('package_id', $subscription->package_id) == $package->id ? 'selected' : ''); ?>>
                                        <?php echo e($package->name); ?> - <?php echo e($package->getFormattedPrice()); ?> (<?php echo e($package->duration_days); ?> يوم)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['package_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">تغيير الباقة قد يؤثر على المدة والمبلغ</div>
                        </div>

                        <div class="row">
                            <!-- تاريخ البداية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="start_date" name="start_date" 
                                           value="<?php echo e(old('start_date', $subscription->start_date->format('Y-m-d'))); ?>" required>
                                    <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- تاريخ الانتهاء -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">تاريخ الانتهاء <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="end_date" name="end_date" 
                                           value="<?php echo e(old('end_date', $subscription->end_date->format('Y-m-d'))); ?>" required>
                                    <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="form-text">يمكن تعديل تاريخ الانتهاء يدوياً</div>
                                </div>
                            </div>
                        </div>

                        <!-- المبلغ المدفوع -->
                        <div class="mb-3">
                            <label for="amount_paid" class="form-label">المبلغ المدفوع (ريال) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control <?php $__errorArgs = ['amount_paid'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="amount_paid" name="amount_paid"
                                   value="<?php echo e(old('amount_paid', $subscription->amount_paid)); ?>"
                                   min="0" step="0.01" required>
                            <?php $__errorArgs = ['amount_paid'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">المبلغ المدفوع للاشتراك</div>
                        </div>

                        <!-- طريقة الدفع -->
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="payment_method" name="payment_method">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash" <?php echo e(old('payment_method', $subscription->payment_method) == 'cash' ? 'selected' : ''); ?>>نقداً</option>
                                <option value="bank_transfer" <?php echo e(old('payment_method', $subscription->payment_method) == 'bank_transfer' ? 'selected' : ''); ?>>تحويل بنكي</option>
                                <option value="credit_card" <?php echo e(old('payment_method', $subscription->payment_method) == 'credit_card' ? 'selected' : ''); ?>>بطاقة ائتمان</option>
                                <option value="mobile_payment" <?php echo e(old('payment_method', $subscription->payment_method) == 'mobile_payment' ? 'selected' : ''); ?>>دفع عبر الجوال</option>
                                <option value="other" <?php echo e(old('payment_method', $subscription->payment_method) == 'other' ? 'selected' : ''); ?>>أخرى</option>
                            </select>
                            <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- الحالة -->
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="status" name="status">
                                <option value="active" <?php echo e(old('status', $subscription->status) == 'active' ? 'selected' : ''); ?>>نشط</option>
                                <option value="expired" <?php echo e(old('status', $subscription->status) == 'expired' ? 'selected' : ''); ?>>منتهي</option>
                                <option value="cancelled" <?php echo e(old('status', $subscription->status) == 'cancelled' ? 'selected' : ''); ?>>ملغي</option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">يمكن تغيير حالة الاشتراك يدوياً</div>
                        </div>

                        <!-- الملاحظات -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="notes" name="notes" rows="3"><?php echo e(old('notes', $subscription->notes)); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">أي ملاحظات إضافية حول الاشتراك</div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                            <a href="<?php echo e(route('admin.subscriptions.show', $subscription)); ?>" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                            </a>
                            <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <!-- معاينة الاشتراك -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معاينة التعديلات</h5>
                </div>
                <div class="card-body">
                    <div id="subscription-preview" class="border rounded p-3 bg-light">
                        <div class="mb-3">
                            <h6 class="text-primary mb-1">الباقة</h6>
                            <p class="text-muted mb-0" id="preview-package-name"><?php echo e($subscription->package->name); ?></p>
                        </div>
                        
                        <div class="mb-3">
                            <h6 class="text-primary mb-1">المدة</h6>
                            <div class="d-flex justify-content-between">
                                <span>من:</span>
                                <span id="preview-start-date"><?php echo e($subscription->start_date->format('Y-m-d')); ?></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>إلى:</span>
                                <span id="preview-end-date"><?php echo e($subscription->end_date->format('Y-m-d')); ?></span>
                            </div>
                            <div class="d-flex justify-content-between fw-bold">
                                <span>المدة:</span>
                                <span id="preview-duration"><?php echo e($subscription->start_date->diffInDays($subscription->end_date)); ?> يوم</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h6 class="text-primary mb-1">المبلغ</h6>
                            <p class="text-success fw-bold mb-0" id="preview-amount-value"><?php echo e(number_format($subscription->amount, 2)); ?> ريال</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6 class="text-primary mb-1">الحالة</h6>
                            <span class="badge bg-<?php echo e($subscription->getStatusClass()); ?>" id="preview-status"><?php echo e($subscription->getStatusLabel()); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الاشتراك -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">إحصائيات الاشتراك</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-0"><?php echo e($subscription->start_date->diffInDays(now())); ?></h4>
                                <small class="text-muted">يوم منذ البداية</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-<?php echo e($subscription->isActive() ? 'success' : 'danger'); ?> mb-0">
                                <?php echo e($subscription->getDaysRemaining()); ?>

                            </h4>
                            <small class="text-muted">يوم متبقي</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تحذيرات -->
            <div class="card mt-3">
                <div class="card-header bg-warning">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات مهمة</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0 small">
                        <li>تأكد من صحة التواريخ المدخلة</li>
                        <li>تغيير الباقة قد يؤثر على الميزات المتاحة</li>
                        <li>تعديل المبلغ لا يؤثر على المدفوعات السابقة</li>
                        <li>تغيير الحالة إلى "ملغي" سيوقف الاشتراك فوراً</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // تحديث المعاينة عند تغيير القيم
    function updatePreview() {
        // الباقة
        const selectedPackage = $('#package_id option:selected');
        $('#preview-package-name').text(selectedPackage.text());

        // التواريخ
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        
        $('#preview-start-date').text(startDate);
        $('#preview-end-date').text(endDate);
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            $('#preview-duration').text(diffDays + ' يوم');
        }

        // المبلغ
        const amount = parseFloat($('#amount_paid').val()) || 0;
        $('#preview-amount-value').text(amount.toFixed(2) + ' ريال');

        // الحالة
        const status = $('#status').val();
        const statusLabels = {
            'active': 'نشط',
            'expired': 'منتهي',
            'cancelled': 'ملغي'
        };
        const statusClasses = {
            'active': 'success',
            'expired': 'warning',
            'cancelled': 'danger'
        };
        
        $('#preview-status')
            .removeClass('bg-success bg-warning bg-danger')
            .addClass('bg-' + statusClasses[status])
            .text(statusLabels[status]);
    }

    // ربط الأحداث
    $('#package_id, #start_date, #end_date, #amount_paid, #status').on('change input', updatePreview);

    // تحديث تاريخ الانتهاء عند تغيير الباقة أو تاريخ البداية
    $('#package_id, #start_date').on('change', function() {
        const startDate = $('#start_date').val();
        const selectedPackage = $('#package_id option:selected');
        const duration = selectedPackage.data('duration');
        
        if (startDate && duration) {
            const start = new Date(startDate);
            const end = new Date(start);
            end.setDate(start.getDate() + parseInt(duration));
            
            const endDateString = end.toISOString().split('T')[0];
            $('#end_date').val(endDateString);
            updatePreview();
        }
    });

    // تحديث المعاينة عند تحميل الصفحة
    updatePreview();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/subscriptions/edit.blade.php ENDPATH**/ ?>