<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            // إعدادات عامة للموقع
            [
                'key' => 'site_name',
                'value' => 'دليل - منصة إعلانات السيارات',
                'type' => 'text',
                'group' => 'general',
                'label' => 'اسم الموقع',
                'description' => 'اسم الموقع الذي يظهر في العنوان والشعار'
            ],
            [
                'key' => 'site_description',
                'value' => 'أفضل منصة لبيع وشراء السيارات في اليمن',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'وصف الموقع',
                'description' => 'وصف مختصر للموقع يظهر في محركات البحث'
            ],
            [
                'key' => 'site_keywords',
                'value' => 'سيارات، بيع، شراء، اليمن، إعلانات، معارض',
                'type' => 'text',
                'group' => 'general',
                'label' => 'كلمات مفتاحية',
                'description' => 'كلمات مفتاحية للموقع مفصولة بفواصل'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'general',
                'label' => 'بريد التواصل',
                'description' => 'البريد الإلكتروني الرئيسي للتواصل'
            ],
            [
                'key' => 'contact_phone',
                'value' => '************',
                'type' => 'text',
                'group' => 'general',
                'label' => 'رقم التواصل',
                'description' => 'رقم الهاتف الرئيسي للتواصل'
            ],
            [
                'key' => 'site_logo',
                'value' => 'logo.png',
                'type' => 'file',
                'group' => 'general',
                'label' => 'شعار الموقع',
                'description' => 'شعار الموقع الرئيسي'
            ],

            // إعدادات الإعلانات
            [
                'key' => 'ad_approval_required',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'ads',
                'label' => 'مراجعة الإعلانات مطلوبة',
                'description' => 'هل تحتاج الإعلانات لموافقة المدير قبل النشر؟'
            ],
            [
                'key' => 'max_free_ads_per_user',
                'value' => '1',
                'type' => 'number',
                'group' => 'ads',
                'label' => 'عدد الإعلانات المجانية',
                'description' => 'عدد الإعلانات المجانية المسموحة لكل مستخدم'
            ],
            [
                'key' => 'free_ad_duration_days',
                'value' => '7',
                'type' => 'number',
                'group' => 'ads',
                'label' => 'مدة الإعلان المجاني',
                'description' => 'مدة الإعلان المجاني بالأيام'
            ],
            [
                'key' => 'featured_ad_boost_percentage',
                'value' => '300',
                'type' => 'number',
                'group' => 'ads',
                'label' => 'نسبة تعزيز الإعلان المميز',
                'description' => 'نسبة ظهور الإعلان المميز أكثر من العادي (%)'
            ],
            [
                'key' => 'auto_delete_expired_ads',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'ads',
                'label' => 'حذف الإعلانات المنتهية تلقائياً',
                'description' => 'حذف الإعلانات المنتهية الصلاحية تلقائياً'
            ],

            // إعدادات الدفع
            [
                'key' => 'payment_methods',
                'value' => '["bank_transfer", "mobile_money", "cash"]',
                'type' => 'json',
                'group' => 'payment',
                'label' => 'طرق الدفع المتاحة',
                'description' => 'طرق الدفع المتاحة في النظام'
            ],
            [
                'key' => 'bank_account_info',
                'value' => 'البنك الأهلي اليمني - رقم الحساب: *********',
                'type' => 'textarea',
                'group' => 'payment',
                'label' => 'معلومات الحساب البنكي',
                'description' => 'معلومات الحساب البنكي لاستقبال المدفوعات'
            ],
            [
                'key' => 'mobile_money_number',
                'value' => '************',
                'type' => 'text',
                'group' => 'payment',
                'label' => 'رقم المحفظة الإلكترونية',
                'description' => 'رقم المحفظة الإلكترونية لاستقبال المدفوعات'
            ],

            // إعدادات الإشعارات
            [
                'key' => 'email_notifications_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'تفعيل الإشعارات بالبريد',
                'description' => 'تفعيل إرسال الإشعارات عبر البريد الإلكتروني'
            ],
            [
                'key' => 'sms_notifications_enabled',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'تفعيل الإشعارات بالرسائل',
                'description' => 'تفعيل إرسال الإشعارات عبر الرسائل النصية'
            ],
            [
                'key' => 'notification_email_from',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'notifications',
                'label' => 'بريد الإرسال',
                'description' => 'البريد الإلكتروني المستخدم لإرسال الإشعارات'
            ],

            // إعدادات الأمان
            [
                'key' => 'max_login_attempts',
                'value' => '5',
                'type' => 'number',
                'group' => 'security',
                'label' => 'عدد محاولات الدخول',
                'description' => 'عدد محاولات الدخول المسموحة قبل الحظر'
            ],
            [
                'key' => 'lockout_duration_minutes',
                'value' => '15',
                'type' => 'number',
                'group' => 'security',
                'label' => 'مدة الحظر بالدقائق',
                'description' => 'مدة حظر المستخدم بعد تجاوز محاولات الدخول'
            ],
            [
                'key' => 'require_email_verification',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'security',
                'label' => 'تأكيد البريد مطلوب',
                'description' => 'هل يجب تأكيد البريد الإلكتروني عند التسجيل؟'
            ],

            // إعدادات الملفات
            [
                'key' => 'max_image_size_mb',
                'value' => '5',
                'type' => 'number',
                'group' => 'files',
                'label' => 'حد أقصى لحجم الصورة (MB)',
                'description' => 'الحد الأقصى لحجم الصورة بالميجابايت'
            ],
            [
                'key' => 'max_video_size_mb',
                'value' => '50',
                'type' => 'number',
                'group' => 'files',
                'label' => 'حد أقصى لحجم الفيديو (MB)',
                'description' => 'الحد الأقصى لحجم الفيديو بالميجابايت'
            ],
            [
                'key' => 'allowed_image_types',
                'value' => '["jpg", "jpeg", "png", "gif", "webp"]',
                'type' => 'json',
                'group' => 'files',
                'label' => 'أنواع الصور المسموحة',
                'description' => 'امتدادات الصور المسموحة للرفع'
            ],
            [
                'key' => 'allowed_video_types',
                'value' => '["mp4", "avi", "mov", "wmv"]',
                'type' => 'json',
                'group' => 'files',
                'label' => 'أنواع الفيديو المسموحة',
                'description' => 'امتدادات الفيديو المسموحة للرفع'
            ],

            // إعدادات SEO
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'معرف Google Analytics',
                'description' => 'معرف Google Analytics لتتبع الزوار'
            ],
            [
                'key' => 'google_ads_id',
                'value' => '',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'معرف Google Ads',
                'description' => 'معرف Google Ads للإعلانات'
            ],
            [
                'key' => 'facebook_pixel_id',
                'value' => '',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'معرف Facebook Pixel',
                'description' => 'معرف Facebook Pixel لتتبع التحويلات'
            ],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }

        $this->command->info('تم إنشاء إعدادات النظام بنجاح');
        $this->command->info('تم إنشاء ' . count($settings) . ' إعداد موزعة على 7 مجموعات');
    }
}
