<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Favorite extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'advertisement_id',
    ];

    // العلاقات
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function advertisement()
    {
        return $this->belongsTo(Advertisement::class);
    }

    // دوال مساعدة
    public static function toggle($userId, $advertisementId)
    {
        $favorite = static::where('user_id', $userId)
                          ->where('advertisement_id', $advertisementId)
                          ->first();

        if ($favorite) {
            $favorite->delete();
            return false; // تم الحذف
        } else {
            static::create([
                'user_id' => $userId,
                'advertisement_id' => $advertisementId,
            ]);
            return true; // تم الإضافة
        }
    }

    public static function isFavorited($userId, $advertisementId)
    {
        return static::where('user_id', $userId)
                    ->where('advertisement_id', $advertisementId)
                    ->exists();
    }
}
