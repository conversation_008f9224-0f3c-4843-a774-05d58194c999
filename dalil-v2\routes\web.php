<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\AdvertisementController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\PackageController;
use App\Http\Controllers\Admin\SubscriptionController;

Route::get('/', function () {
    return view('welcome');
});

// مسارات المصادقة الإدارية
Route::prefix('admin')->name('admin.')->group(function () {
    // مسارات غير محمية (تسجيل الدخول)
    Route::middleware('guest')->group(function () {
        Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AuthController::class, 'login']);
    });

    // مسارات محمية (تحتاج تسجيل دخول إداري)
    Route::middleware('admin')->group(function () {
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // إدارة المستخدمين
        Route::resource('users', UserController::class);
        Route::get('users-data', [UserController::class, 'getUsersData'])->name('users.data');
        Route::patch('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::patch('users/{user}/suspend', [UserController::class, 'suspend'])->name('users.suspend');

        // إدارة الأدوار والصلاحيات
        Route::resource('roles', RoleController::class);
        Route::get('roles-data', [RoleController::class, 'getRolesData'])->name('roles.data');
        Route::post('roles/{role}/permissions', [RoleController::class, 'updatePermissions'])->name('roles.permissions.update');

        Route::resource('permissions', PermissionController::class);
        Route::get('permissions-data', [PermissionController::class, 'getPermissionsData'])->name('permissions.data');

        // إدارة الباقات
        Route::resource('packages', PackageController::class);
        Route::get('packages-data', [PackageController::class, 'getPackagesData'])->name('packages.data');
        Route::patch('packages/{package}/toggle-status', [PackageController::class, 'toggleStatus'])->name('packages.toggle-status');

        // Subscription management routes
        Route::resource('subscriptions', SubscriptionController::class);
        Route::get('subscriptions-data', [SubscriptionController::class, 'getSubscriptionsData'])->name('subscriptions.data');
        Route::post('subscriptions/{subscription}/renew', [SubscriptionController::class, 'renew'])->name('subscriptions.renew');
        Route::post('subscriptions/{subscription}/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
        Route::post('permissions/create-defaults', [PermissionController::class, 'createDefaultPermissions'])->name('permissions.create-defaults');

        // إدارة الإعلانات
        Route::resource('advertisements', AdvertisementController::class)->only(['index', 'show', 'destroy']);
        Route::get('advertisements-data', [AdvertisementController::class, 'getAdvertisementsData'])->name('advertisements.data');
        Route::patch('advertisements/{advertisement}/status', [AdvertisementController::class, 'updateStatus'])->name('advertisements.update-status');
    });
});
