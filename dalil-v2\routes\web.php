<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\AdvertisementController;

Route::get('/', function () {
    return view('welcome');
});

// مسارات المصادقة الإدارية
Route::prefix('admin')->name('admin.')->group(function () {
    // مسارات غير محمية (تسجيل الدخول)
    Route::middleware('guest')->group(function () {
        Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AuthController::class, 'login']);
    });

    // مسارات محمية (تحتاج تسجيل دخول إداري)
    Route::middleware('admin')->group(function () {
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // إدارة المستخدمين
        Route::resource('users', UserController::class);
        Route::get('users-data', [UserController::class, 'getUsersData'])->name('users.data');
        Route::patch('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::patch('users/{user}/suspend', [UserController::class, 'suspend'])->name('users.suspend');

        // إدارة الإعلانات
        Route::resource('advertisements', AdvertisementController::class)->only(['index', 'show', 'destroy']);
        Route::get('advertisements-data', [AdvertisementController::class, 'getAdvertisementsData'])->name('advertisements.data');
        Route::patch('advertisements/{advertisement}/status', [AdvertisementController::class, 'updateStatus'])->name('advertisements.update-status');
    });
});
