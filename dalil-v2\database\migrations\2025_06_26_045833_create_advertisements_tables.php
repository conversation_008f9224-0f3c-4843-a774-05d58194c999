<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول الإعلانات
        Schema::create('advertisements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('manufacturer_id')->constrained();
            $table->foreignId('model_id')->constrained();
            $table->foreignId('governorate_id')->constrained();
            $table->foreignId('district_id')->constrained();
            $table->foreignId('fuel_type_id')->constrained();
            $table->foreignId('transmission_type_id')->constrained();
            $table->foreignId('color_id')->constrained();

            $table->string('title');
            $table->text('description');
            $table->decimal('price', 12, 2);
            $table->boolean('price_negotiable')->default(false);
            $table->integer('year');
            $table->integer('mileage')->nullable();
            $table->enum('condition', ['new', 'used', 'accident']);
            $table->string('engine_size')->nullable();
            $table->integer('doors')->nullable();
            $table->boolean('air_conditioning')->default(false);
            $table->boolean('power_steering')->default(false);
            $table->boolean('abs_brakes')->default(false);
            $table->boolean('airbags')->default(false);
            $table->boolean('sunroof')->default(false);
            $table->boolean('leather_seats')->default(false);
            $table->text('additional_features')->nullable();

            $table->enum('status', ['pending', 'approved', 'rejected', 'expired'])->default('pending');
            $table->text('rejection_reason')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->integer('views_count')->default(0);
            $table->integer('favorites_count')->default(0);
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['status', 'approved_at']);
            $table->index(['user_id', 'status']);
        });

        // جدول صور الإعلانات
        Schema::create('advertisement_images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('advertisement_id')->constrained()->onDelete('cascade');
            $table->string('image_path');
            $table->string('image_name');
            $table->integer('sort_order')->default(0);
            $table->boolean('is_primary')->default(false);
            $table->timestamps();
        });

        // جدول فيديوهات الإعلانات
        Schema::create('advertisement_videos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('advertisement_id')->constrained()->onDelete('cascade');
            $table->string('video_path');
            $table->string('video_name');
            $table->string('thumbnail_path')->nullable();
            $table->integer('duration')->nullable(); // بالثواني
            $table->timestamps();
        });

        // جدول المفضلة
        Schema::create('favorites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('advertisement_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['user_id', 'advertisement_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('favorites');
        Schema::dropIfExists('advertisement_videos');
        Schema::dropIfExists('advertisement_images');
        Schema::dropIfExists('advertisements');
    }
};
