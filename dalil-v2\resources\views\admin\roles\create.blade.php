@extends('admin.layouts.app')

@section('title', 'إضافة دور جديد')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.roles.index') }}">إدارة الأدوار</a></li>
    <li class="breadcrumb-item active">إضافة دور جديد</li>
@endsection

@section('content')
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إضافة دور جديد</h1>
        <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm shadow-sm">
            <i class="fas fa-arrow-right fa-sm text-white-50"></i> العودة للقائمة
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الدور</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.roles.store') }}" method="POST" id="role-form">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="display_name">اسم الدور <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                           id="display_name" name="display_name" value="{{ old('display_name') }}" 
                                           placeholder="مثال: مدير النظام" required>
                                    @error('display_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">الاسم التقني <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" 
                                           placeholder="مثال: admin" required>
                                    <small class="form-text text-muted">
                                        يجب أن يكون باللغة الإنجليزية ولا يحتوي على مسافات
                                    </small>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">الوصف</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="وصف مختصر للدور وصلاحياته">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <label class="mb-0">الصلاحيات</label>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllPermissions()">
                                        تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="deselectAllPermissions()">
                                        إلغاء التحديد
                                    </button>
                                </div>
                            </div>

                            <div class="permissions-container">
                                @foreach($permissions as $group => $groupPermissions)
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 text-primary">{{ $group }}</h6>
                                                <div>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="toggleGroupPermissions('{{ $group }}')">
                                                        تحديد المجموعة
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @foreach($groupPermissions as $permission)
                                                    <div class="col-md-6 col-lg-4">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input permission-checkbox" 
                                                                   type="checkbox" 
                                                                   name="permissions[]" 
                                                                   value="{{ $permission->id }}" 
                                                                   id="permission_{{ $permission->id }}"
                                                                   data-group="{{ $group }}"
                                                                   {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                                                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                                <strong>{{ $permission->display_name }}</strong>
                                                                @if($permission->description)
                                                                    <br><small class="text-muted">{{ $permission->description }}</small>
                                                                @endif
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الدور
                            </button>
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">نصائح</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> نصائح مهمة:</h6>
                        <ul class="mb-0">
                            <li>اختر اسماً واضحاً ومفهوماً للدور</li>
                            <li>الاسم التقني يجب أن يكون باللغة الإنجليزية</li>
                            <li>حدد الصلاحيات بعناية حسب احتياجات المستخدم</li>
                            <li>يمكنك تعديل الصلاحيات لاحقاً</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير:</h6>
                        <p class="mb-0">
                            تأكد من منح الصلاحيات المناسبة فقط لتجنب المشاكل الأمنية.
                        </p>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">الصلاحيات المحددة</h6>
                </div>
                <div class="card-body">
                    <div id="selected-permissions-count" class="text-center">
                        <h4 class="text-primary">0</h4>
                        <p class="text-muted mb-0">صلاحية محددة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث عداد الصلاحيات المحددة
    updateSelectedPermissionsCount();
    
    // مراقبة تغيير الصلاحيات
    $('.permission-checkbox').on('change', function() {
        updateSelectedPermissionsCount();
    });

    // إنشاء الاسم التقني تلقائياً من اسم الدور
    $('#display_name').on('input', function() {
        const displayName = $(this).val();
        const technicalName = displayName
            .toLowerCase()
            .replace(/[أ-ي]/g, '') // إزالة الأحرف العربية
            .replace(/\s+/g, '_') // استبدال المسافات بـ _
            .replace(/[^a-z0-9_]/g, ''); // إزالة الأحرف الخاصة
        
        $('#name').val(technicalName);
    });
});

// دالة تحديد جميع الصلاحيات
function selectAllPermissions() {
    $('.permission-checkbox').prop('checked', true);
    updateSelectedPermissionsCount();
}

// دالة إلغاء تحديد جميع الصلاحيات
function deselectAllPermissions() {
    $('.permission-checkbox').prop('checked', false);
    updateSelectedPermissionsCount();
}

// دالة تحديد/إلغاء تحديد صلاحيات مجموعة
function toggleGroupPermissions(group) {
    const groupCheckboxes = $(`.permission-checkbox[data-group="${group}"]`);
    const checkedCount = groupCheckboxes.filter(':checked').length;
    const totalCount = groupCheckboxes.length;
    
    if (checkedCount === totalCount) {
        // إذا كانت جميع صلاحيات المجموعة محددة، قم بإلغاء التحديد
        groupCheckboxes.prop('checked', false);
    } else {
        // وإلا قم بتحديد جميع صلاحيات المجموعة
        groupCheckboxes.prop('checked', true);
    }
    
    updateSelectedPermissionsCount();
}

// دالة تحديث عداد الصلاحيات المحددة
function updateSelectedPermissionsCount() {
    const selectedCount = $('.permission-checkbox:checked').length;
    $('#selected-permissions-count h4').text(selectedCount);
}
</script>

<style>
.permissions-container .card {
    border: 1px solid #e3e6f0;
}

.permissions-container .card-header {
    border-bottom: 1px solid #e3e6f0;
    padding: 0.75rem 1rem;
}

.form-check {
    padding-right: 1.25rem;
}

.form-check-input {
    margin-right: -1.25rem;
}

.form-check-label {
    padding-right: 0.5rem;
    cursor: pointer;
}

#selected-permissions-count h4 {
    font-size: 2rem;
    font-weight: bold;
}
</style>
@endpush
