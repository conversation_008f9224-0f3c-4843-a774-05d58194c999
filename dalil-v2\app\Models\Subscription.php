<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'package_id',
        'status',
        'start_date',
        'end_date',
        'amount_paid',
        'payment_method',
        'transaction_id',
        'ads_used',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'amount_paid' => 'decimal:2',
    ];

    // العلاقات
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function package()
    {
        return $this->belongsTo(Package::class);
    }

    public function history()
    {
        return $this->hasMany(SubscriptionHistory::class);
    }

    // دوال مساعدة
    public function isActive()
    {
        return $this->status === 'active' && $this->end_date >= now()->toDateString();
    }

    public function isExpired()
    {
        return $this->end_date < now()->toDateString();
    }

    public function canCreateAd()
    {
        return $this->isActive() && $this->ads_used < $this->package->max_ads;
    }

    public function getRemainingAds()
    {
        return max(0, $this->package->max_ads - $this->ads_used);
    }

    public function getDaysRemaining()
    {
        return max(0, now()->diffInDays($this->end_date, false));
    }
}
