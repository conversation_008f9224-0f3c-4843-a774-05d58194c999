<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'package_id',
        'status',
        'start_date',
        'end_date',
        'amount_paid',
        'payment_method',
        'transaction_id',
        'ads_used',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'amount_paid' => 'decimal:2',
    ];

    // العلاقات
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function package()
    {
        return $this->belongsTo(Package::class);
    }

    public function history()
    {
        return $this->hasMany(SubscriptionHistory::class);
    }

    // دوال مساعدة
    public function isActive()
    {
        return $this->status === 'active' && $this->end_date >= now()->toDateString();
    }

    public function isExpired()
    {
        return $this->end_date < now()->toDateString() || $this->status === 'expired';
    }

    public function isExpiringSoon()
    {
        return $this->isActive() && $this->end_date->diffInDays(now()) <= 7;
    }

    public function canCreateAd()
    {
        return $this->isActive() && $this->ads_used < $this->package->max_ads;
    }

    public function getRemainingAds()
    {
        return max(0, $this->package->max_ads - $this->ads_used);
    }

    public function getDaysRemaining()
    {
        if ($this->isExpired()) {
            return 0;
        }
        return max(0, $this->end_date->diffInDays(now()));
    }

    public function getFormattedAmount()
    {
        return number_format($this->amount_paid, 2) . ' ريال';
    }

    public function getStatusLabel()
    {
        $labels = [
            'active' => 'نشط',
            'expired' => 'منتهي',
            'cancelled' => 'ملغي',
        ];

        return $labels[$this->status] ?? $this->status;
    }

    public function getStatusClass()
    {
        $classes = [
            'active' => 'success',
            'expired' => 'warning',
            'cancelled' => 'danger',
        ];

        return $classes[$this->status] ?? 'secondary';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')->where('end_date', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')->orWhere('end_date', '<=', now());
    }

    public function scopeExpiringSoon($query)
    {
        return $query->where('status', 'active')
                    ->where('end_date', '>', now())
                    ->where('end_date', '<=', now()->addDays(7));
    }
}
