<?php $__env->startSection('title', 'تفاصيل الباقة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تفاصيل الباقة: <?php echo e($package->name); ?></h1>
            <p class="text-muted">عرض معلومات وإحصائيات الباقة</p>
        </div>
        <div>
            <a href="<?php echo e(route('admin.packages.edit', $package)); ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>تعديل الباقة
            </a>
            <a href="<?php echo e(route('admin.packages.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الباقة -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الباقة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">اسم الباقة:</td>
                                    <td><?php echo e($package->name); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">السعر:</td>
                                    <td>
                                        <span class="badge <?php echo e($package->price > 0 ? 'bg-success' : 'bg-secondary'); ?> fs-6">
                                            <?php echo e($package->getFormattedPrice()); ?>

                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">مدة الباقة:</td>
                                    <td><?php echo e($package->duration_days); ?> يوم</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">عدد الإعلانات:</td>
                                    <td><?php echo e($package->max_ads); ?> إعلان</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الصور لكل إعلان:</td>
                                    <td><?php echo e($package->max_images_per_ad); ?> صورة</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">الإعلانات المميزة:</td>
                                    <td>
                                        <?php if($package->featured_ads): ?>
                                            <span class="badge bg-warning text-dark">متاحة</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير متاحة</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">السماح بالفيديو:</td>
                                    <td>
                                        <?php if($package->video_allowed): ?>
                                            <span class="badge bg-info">متاح</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير متاح</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">ترتيب العرض:</td>
                                    <td><?php echo e($package->sort_order); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">حالة الباقة:</td>
                                    <td>
                                        <?php if($package->is_active): ?>
                                            <span class="badge bg-success">نشطة</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير نشطة</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ الإنشاء:</td>
                                    <td><?php echo e($package->created_at->format('Y-m-d H:i')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if($package->description): ?>
                    <div class="mt-3">
                        <h6>وصف الباقة:</h6>
                        <p class="text-muted"><?php echo e($package->description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- إحصائيات الاشتراكات -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">إحصائيات الاشتراكات</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-primary mb-0"><?php echo e($package->subscriptions()->count()); ?></h3>
                                <small class="text-muted">إجمالي الاشتراكات</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-success mb-0"><?php echo e($package->subscriptions()->where('status', 'active')->count()); ?></h3>
                                <small class="text-muted">اشتراكات نشطة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-warning mb-0"><?php echo e($package->subscriptions()->where('status', 'expired')->count()); ?></h3>
                                <small class="text-muted">اشتراكات منتهية</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-info mb-0"><?php echo e(number_format($package->subscriptions()->sum('amount'), 2)); ?></h3>
                            <small class="text-muted">إجمالي الإيرادات (ريال)</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر الاشتراكات -->
            <?php if($package->subscriptions()->count() > 0): ?>
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخر الاشتراكات</h5>
                    <small class="text-muted">آخر 10 اشتراكات</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $package->subscriptions()->with('user')->latest()->limit(10)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo e($subscription->user->name); ?></div>
                                                <small class="text-muted"><?php echo e($subscription->user->email); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo e($subscription->start_date->format('Y-m-d')); ?></td>
                                    <td><?php echo e($subscription->end_date->format('Y-m-d')); ?></td>
                                    <td><?php echo e(number_format($subscription->amount, 2)); ?> ريال</td>
                                    <td>
                                        <?php switch($subscription->status):
                                            case ('active'): ?>
                                                <span class="badge bg-success">نشط</span>
                                                <?php break; ?>
                                            <?php case ('expired'): ?>
                                                <span class="badge bg-warning">منتهي</span>
                                                <?php break; ?>
                                            <?php case ('cancelled'): ?>
                                                <span class="badge bg-danger">ملغي</span>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <span class="badge bg-secondary"><?php echo e($subscription->status); ?></span>
                                        <?php endswitch; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- معاينة الباقة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معاينة الباقة</h5>
                </div>
                <div class="card-body">
                    <div class="border rounded p-3 bg-light">
                        <h6 class="text-primary"><?php echo e($package->name); ?></h6>
                        <?php if($package->description): ?>
                            <p class="text-muted small"><?php echo e($package->description); ?></p>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">السعر:</span>
                            <span class="text-success fw-bold"><?php echo e($package->getFormattedPrice()); ?></span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>المدة:</span>
                            <span><?php echo e($package->duration_days); ?> يوم</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>عدد الإعلانات:</span>
                            <span><?php echo e($package->max_ads); ?></span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الصور لكل إعلان:</span>
                            <span><?php echo e($package->max_images_per_ad); ?></span>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">المميزات الإضافية:</small>
                            <div class="mt-1">
                                <?php if($package->featured_ads || $package->video_allowed): ?>
                                    <?php if($package->featured_ads): ?>
                                        <span class="badge bg-warning text-dark me-1">مميزة</span>
                                    <?php endif; ?>
                                    <?php if($package->video_allowed): ?>
                                        <span class="badge bg-info me-1">فيديو</span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">لا توجد مميزات إضافية</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.packages.edit', $package)); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>تعديل الباقة
                        </a>
                        
                        <form action="<?php echo e(route('admin.packages.toggle-status', $package)); ?>" method="POST" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <button type="submit" class="btn btn-outline-<?php echo e($package->is_active ? 'warning' : 'success'); ?> btn-sm w-100">
                                <i class="fas fa-<?php echo e($package->is_active ? 'pause' : 'play'); ?> me-2"></i>
                                <?php echo e($package->is_active ? 'إيقاف الباقة' : 'تفعيل الباقة'); ?>

                            </button>
                        </form>

                        <?php if($package->subscriptions()->where('status', 'active')->count() == 0): ?>
                        <form action="<?php echo e(route('admin.packages.destroy', $package)); ?>" method="POST" 
                              onsubmit="return confirm('هل أنت متأكد من حذف هذه الباقة؟')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash me-2"></i>حذف الباقة
                            </button>
                        </form>
                        <?php else: ?>
                        <button class="btn btn-outline-danger btn-sm w-100" disabled title="لا يمكن حذف باقة لديها اشتراكات نشطة">
                            <i class="fas fa-trash me-2"></i>حذف الباقة
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle text-info me-2"></i>معلومات إضافية</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0 small">
                        <li class="mb-2">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            تم الإنشاء: <?php echo e($package->created_at->format('Y-m-d H:i')); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-edit text-muted me-2"></i>
                            آخر تحديث: <?php echo e($package->updated_at->format('Y-m-d H:i')); ?>

                        </li>
                        <li class="mb-0">
                            <i class="fas fa-sort text-muted me-2"></i>
                            ترتيب العرض: <?php echo e($package->sort_order); ?>

                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/packages/show.blade.php ENDPATH**/ ?>