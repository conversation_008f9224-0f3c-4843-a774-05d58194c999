/**
 * تنسيقات الجداول التفاعلية مع AJAX
 */

/* تنسيقات الأعمدة القابلة للترتيب */
.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.sortable:hover {
    background-color: #f8f9fc !important;
}

.sortable i {
    margin-left: 5px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.sortable:hover i {
    opacity: 1;
}

/* مؤشر التحميل */
#loading-indicator {
    color: #5a5c69;
    font-size: 14px;
}

.loading-overlay {
    position: relative;
}

.loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* تحسينات الجدول */
.table-responsive {
    border-radius: 0.35rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fc;
    color: #5a5c69;
    font-size: 0.85rem;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-color: #e3e6f0;
}

/* تنسيقات البحث والفلاتر */
.filters-card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* تنسيقات الترقيم */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #4e73df;
    border-color: #dddfeb;
}

.page-link:hover {
    color: #224abe;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

.page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* تنسيقات الشارات */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-sm {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
}

/* تنسيقات القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.dropdown-item {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
}

.dropdown-item i {
    width: 16px;
    margin-left: 8px;
}

/* تنسيقات الأيقونات الدائرية */
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تنسيقات الحالة الفارغة */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: #858796;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تنسيقات حالة الخطأ */
.error-state {
    padding: 2rem 1rem;
    text-align: center;
    color: #e74a3b;
}

.error-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
    
    .dropdown-menu {
        font-size: 0.8rem;
    }
    
    .icon-circle {
        height: 2rem;
        width: 2rem;
    }
}

/* تنسيقات التنبيهات */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-size: 0.85rem;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* تحسينات الأزرار */
.btn-sm {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

.btn-outline-primary {
    border-color: #4e73df;
    color: #4e73df;
}

.btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* تنسيقات العدادات */
.counter-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
}

/* تحسينات الفورم */
.form-group label {
    font-weight: 600;
    color: #5a5c69;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.form-control {
    font-size: 0.85rem;
    border-color: #d1d3e2;
}

.form-control::placeholder {
    color: #858796;
    opacity: 1;
}

/* تنسيقات خاصة بالنصوص العربية */
.text-ar {
    direction: rtl;
    text-align: right;
}

/* تحسينات الطباعة */
@media print {
    .card-header,
    .pagination,
    .dropdown,
    #loading-indicator,
    .filters-card {
        display: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .badge {
        border: 1px solid #000;
        color: #000 !important;
        background: transparent !important;
    }
}
