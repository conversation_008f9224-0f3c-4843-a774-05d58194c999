<?php $__env->startSection('title', 'تفاصيل الاشتراك'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تفاصيل الاشتراك</h1>
            <p class="text-muted">عرض تفاصيل اشتراك <?php echo e($subscription->user->name); ?></p>
        </div>
        <div>
            <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
            </a>
            <?php if($subscription->canBeEdited()): ?>
                <a href="<?php echo e(route('admin.subscriptions.edit', $subscription)); ?>" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- حالة الاشتراك -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-<?php echo e($subscription->getStatusClass()); ?>">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="avatar-lg bg-<?php echo e($subscription->getStatusClass()); ?> rounded-circle d-flex align-items-center justify-content-center me-3">
                                    <i class="fas fa-<?php echo e($subscription->status === 'active' ? 'check' : ($subscription->status === 'expired' ? 'clock' : 'ban')); ?> fa-2x text-white"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1"><?php echo e($subscription->getStatusLabel()); ?></h4>
                                    <p class="text-muted mb-0">
                                        <?php if($subscription->isActive()): ?>
                                            الاشتراك نشط ومتاح للاستخدام
                                        <?php elseif($subscription->isExpired()): ?>
                                            انتهت صلاحية الاشتراك في <?php echo e($subscription->end_date->format('Y-m-d')); ?>

                                        <?php else: ?>
                                            تم إلغاء الاشتراك
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <?php if($subscription->isActive()): ?>
                                <h3 class="text-<?php echo e($subscription->isExpiringSoon() ? 'warning' : 'success'); ?> mb-0">
                                    <?php echo e($subscription->getDaysRemaining()); ?> يوم
                                </h3>
                                <small class="text-muted">متبقي من الاشتراك</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الاشتراك -->
        <div class="col-lg-8">
            <!-- بيانات المستخدم -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>بيانات المستخدم</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-md bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo e($subscription->user->name); ?></h6>
                                    <small class="text-muted"><?php echo e($subscription->user->email); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">تاريخ التسجيل</small>
                                    <div class="fw-bold"><?php echo e($subscription->user->created_at->format('Y-m-d')); ?></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">إجمالي الاشتراكات</small>
                                    <div class="fw-bold"><?php echo e($subscription->user->subscriptions()->count()); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الاشتراك -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>تفاصيل الاشتراك</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">الباقة</label>
                                <div class="fw-bold"><?php echo e($subscription->package->name); ?></div>
                                <small class="text-muted"><?php echo e($subscription->package->description); ?></small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">المبلغ المدفوع</label>
                                <div class="fw-bold text-success"><?php echo e($subscription->getFormattedAmount()); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">تاريخ البداية</label>
                                <div class="fw-bold"><?php echo e($subscription->start_date->format('Y-m-d')); ?></div>
                                <small class="text-muted"><?php echo e($subscription->start_date->diffForHumans()); ?></small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">تاريخ الانتهاء</label>
                                <div class="fw-bold"><?php echo e($subscription->end_date->format('Y-m-d')); ?></div>
                                <small class="text-muted"><?php echo e($subscription->end_date->diffForHumans()); ?></small>
                            </div>
                        </div>
                        <?php if($subscription->payment_method): ?>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">طريقة الدفع</label>
                                    <div class="fw-bold">
                                        <?php switch($subscription->payment_method):
                                            case ('cash'): ?>
                                                نقداً
                                                <?php break; ?>
                                            <?php case ('bank_transfer'): ?>
                                                تحويل بنكي
                                                <?php break; ?>
                                            <?php case ('credit_card'): ?>
                                                بطاقة ائتمان
                                                <?php break; ?>
                                            <?php case ('mobile_payment'): ?>
                                                دفع عبر الجوال
                                                <?php break; ?>
                                            <?php default: ?>
                                                <?php echo e($subscription->payment_method); ?>

                                        <?php endswitch; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">مدة الاشتراك</label>
                                <div class="fw-bold"><?php echo e($subscription->start_date->diffInDays($subscription->end_date)); ?> يوم</div>
                            </div>
                        </div>
                        <?php if($subscription->notes): ?>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">الملاحظات</label>
                                    <div class="border rounded p-2 bg-light"><?php echo e($subscription->notes); ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- ميزات الباقة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>ميزات الباقة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-ad fa-2x text-primary mb-2"></i>
                                <h4 class="mb-0"><?php echo e($subscription->package->max_ads); ?></h4>
                                <small class="text-muted">إعلان كحد أقصى</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-images fa-2x text-success mb-2"></i>
                                <h4 class="mb-0"><?php echo e($subscription->package->max_images_per_ad); ?></h4>
                                <small class="text-muted">صورة لكل إعلان</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                                <h4 class="mb-0"><?php echo e($subscription->package->duration_days); ?></h4>
                                <small class="text-muted">يوم مدة الباقة</small>
                            </div>
                        </div>
                    </div>
                    
                    <?php if($subscription->package->features): ?>
                        <div class="mt-3">
                            <h6>الميزات الإضافية:</h6>
                            <ul class="list-unstyled">
                                <?php $__currentLoopData = json_decode($subscription->package->features, true) ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><i class="fas fa-check text-success me-2"></i><?php echo e($feature); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- إجراءات سريعة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if($subscription->canBeRenewed()): ?>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#renewModal">
                                <i class="fas fa-redo me-2"></i>تجديد الاشتراك
                            </button>
                        <?php endif; ?>
                        
                        <?php if($subscription->canBeCancelled()): ?>
                            <button class="btn btn-warning" onclick="cancelSubscription(<?php echo e($subscription->id); ?>)">
                                <i class="fas fa-ban me-2"></i>إلغاء الاشتراك
                            </button>
                        <?php endif; ?>
                        
                        <?php if($subscription->canBeEdited()): ?>
                            <a href="<?php echo e(route('admin.subscriptions.edit', $subscription)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>تعديل الاشتراك
                            </a>
                        <?php endif; ?>
                        
                        <?php if($subscription->canBeDeleted()): ?>
                            <button class="btn btn-outline-danger" onclick="deleteSubscription(<?php echo e($subscription->id); ?>)">
                                <i class="fas fa-trash me-2"></i>حذف الاشتراك
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">إحصائيات الاستخدام</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-0"><?php echo e($subscription->start_date->diffInDays(now())); ?></h4>
                                <small class="text-muted">يوم منذ البداية</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-<?php echo e($subscription->isActive() ? 'success' : 'danger'); ?> mb-0">
                                <?php echo e($subscription->getDaysRemaining()); ?>

                            </h4>
                            <small class="text-muted">يوم متبقي</small>
                        </div>
                    </div>
                    
                    <?php if($subscription->isActive()): ?>
                        <div class="mt-3">
                            <div class="progress">
                                <?php
                                    $totalDays = $subscription->start_date->diffInDays($subscription->end_date);
                                    $usedDays = $subscription->start_date->diffInDays(now());
                                    $percentage = $totalDays > 0 ? ($usedDays / $totalDays) * 100 : 0;
                                ?>
                                <div class="progress-bar bg-<?php echo e($percentage > 80 ? 'warning' : 'success'); ?>" 
                                     style="width: <?php echo e(min($percentage, 100)); ?>%"></div>
                            </div>
                            <small class="text-muted"><?php echo e(number_format($percentage, 1)); ?>% من المدة مستخدمة</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معلومات النظام</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">تاريخ الإنشاء:</span>
                            <span><?php echo e($subscription->created_at->format('Y-m-d H:i')); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">آخر تحديث:</span>
                            <span><?php echo e($subscription->updated_at->format('Y-m-d H:i')); ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">رقم الاشتراك:</span>
                            <span class="font-monospace">#<?php echo e($subscription->id); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تجديد الاشتراك -->
<div class="modal fade" id="renewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تجديد الاشتراك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="renew-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="duration_days" class="form-label">مدة التجديد (بالأيام)</label>
                        <input type="number" class="form-control" id="duration_days" name="duration_days" 
                               value="<?php echo e($subscription->package->duration_days); ?>" min="1" max="365" required>
                    </div>
                    <div class="mb-3">
                        <label for="renew_amount" class="form-label">مبلغ التجديد</label>
                        <input type="number" class="form-control" id="renew_amount" name="amount_paid"
                               value="<?php echo e($subscription->package->price); ?>" min="0" step="0.01" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">تجديد الاشتراك</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// تجديد الاشتراك
$('#renew-form').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    $.ajax({
        url: `/admin/subscriptions/<?php echo e($subscription->id); ?>/renew`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#renewModal').modal('hide');
                location.reload();
            } else {
                alert('حدث خطأ: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('حدث خطأ في تجديد الاشتراك');
        }
    });
});

// إلغاء الاشتراك
function cancelSubscription(subscriptionId) {
    if (!confirm('هل أنت متأكد من إلغاء هذا الاشتراك؟')) return;
    
    $.ajax({
        url: `/admin/subscriptions/${subscriptionId}/cancel`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('حدث خطأ في إلغاء الاشتراك');
        }
    });
}

// حذف الاشتراك
function deleteSubscription(subscriptionId) {
    if (!confirm('هل أنت متأكد من حذف هذا الاشتراك؟ هذا الإجراء لا يمكن التراجع عنه.')) return;
    
    $.ajax({
        url: `/admin/subscriptions/${subscriptionId}`,
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                window.location.href = '<?php echo e(route("admin.subscriptions.index")); ?>';
            } else {
                alert('حدث خطأ: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('حدث خطأ في حذف الاشتراك');
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/subscriptions/show.blade.php ENDPATH**/ ?>