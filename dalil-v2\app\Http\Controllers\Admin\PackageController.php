<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PackageController extends Controller
{
    /**
     * عرض قائمة الباقات
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->getPackagesData($request);
        }

        return view('admin.packages.index');
    }

    /**
     * جلب بيانات الباقات عبر AJAX
     */
    public function getPackagesData(Request $request)
    {
        $query = Package::withCount('subscriptions');

        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // فلتر الحالة
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // فلتر السعر
        if ($request->filled('price_range')) {
            $priceRange = $request->get('price_range');
            switch ($priceRange) {
                case 'free':
                    $query->where('price', 0);
                    break;
                case 'low':
                    $query->whereBetween('price', [0.01, 50]);
                    break;
                case 'medium':
                    $query->whereBetween('price', [50.01, 200]);
                    break;
                case 'high':
                    $query->where('price', '>', 200);
                    break;
            }
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortDir = $request->get('sort_dir', 'asc');

        if (in_array($sortBy, ['id', 'name', 'price', 'duration_days', 'max_ads', 'subscriptions_count', 'created_at', 'sort_order'])) {
            if ($sortBy === 'subscriptions_count') {
                $query->orderBy('subscriptions_count', $sortDir);
            } else {
                $query->orderBy($sortBy, $sortDir);
            }
        }

        $packages = $query->paginate($request->get('per_page', 25));

        return response()->json([
            'success' => true,
            'data' => $packages->items(),
            'pagination' => [
                'current_page' => $packages->currentPage(),
                'last_page' => $packages->lastPage(),
                'per_page' => $packages->perPage(),
                'total' => $packages->total(),
                'from' => $packages->firstItem(),
                'to' => $packages->lastItem(),
            ],
            'links' => $packages->appends(request()->query())->links()->render()
        ]);
    }

    /**
     * عرض نموذج إنشاء باقة جديدة
     */
    public function create()
    {
        return view('admin.packages.create');
    }

    /**
     * حفظ باقة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:packages,name',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'max_ads' => 'required|integer|min:1',
            'max_images_per_ad' => 'required|integer|min:1|max:20',
            'featured_ads' => 'boolean',
            'video_allowed' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ], [
            'name.required' => 'اسم الباقة مطلوب',
            'name.unique' => 'اسم الباقة موجود مسبقاً',
            'price.required' => 'سعر الباقة مطلوب',
            'price.numeric' => 'سعر الباقة يجب أن يكون رقماً',
            'price.min' => 'سعر الباقة لا يمكن أن يكون أقل من صفر',
            'duration_days.required' => 'مدة الباقة مطلوبة',
            'duration_days.integer' => 'مدة الباقة يجب أن تكون رقماً صحيحاً',
            'duration_days.min' => 'مدة الباقة يجب أن تكون يوم واحد على الأقل',
            'max_ads.required' => 'عدد الإعلانات المسموح مطلوب',
            'max_ads.integer' => 'عدد الإعلانات يجب أن يكون رقماً صحيحاً',
            'max_ads.min' => 'عدد الإعلانات يجب أن يكون واحد على الأقل',
            'max_images_per_ad.required' => 'عدد الصور لكل إعلان مطلوب',
            'max_images_per_ad.integer' => 'عدد الصور يجب أن يكون رقماً صحيحاً',
            'max_images_per_ad.min' => 'عدد الصور يجب أن يكون واحد على الأقل',
            'max_images_per_ad.max' => 'عدد الصور لا يمكن أن يزيد عن 20 صورة',
        ]);

        try {
            DB::beginTransaction();

            $package = Package::create([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'duration_days' => $request->duration_days,
                'max_ads' => $request->max_ads,
                'max_images_per_ad' => $request->max_images_per_ad,
                'featured_ads' => $request->boolean('featured_ads'),
                'video_allowed' => $request->boolean('video_allowed'),
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->sort_order ?? 0,
            ]);

            DB::commit();

            return redirect()->route('admin.packages.index')
                ->with('success', 'تم إنشاء الباقة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء الباقة')
                ->withInput();
        }
    }

    /**
     * عرض تفاصيل باقة محددة
     */
    public function show(Package $package)
    {
        $package->load(['subscriptions.user']);

        // إحصائيات الباقة
        $stats = [
            'total_subscriptions' => $package->subscriptions()->count(),
            'active_subscriptions' => $package->subscriptions()->where('status', 'active')->count(),
            'expired_subscriptions' => $package->subscriptions()->where('status', 'expired')->count(),
            'cancelled_subscriptions' => $package->subscriptions()->where('status', 'cancelled')->count(),
            'total_revenue' => $package->subscriptions()->sum('amount_paid'),
            'monthly_revenue' => $package->subscriptions()
                ->whereRaw("strftime('%Y-%m', created_at) = ?", [date('Y-m')])
                ->sum('amount_paid'),
        ];

        // أحدث الاشتراكات
        $recentSubscriptions = $package->subscriptions()
            ->with('user')
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.packages.show', compact('package', 'stats', 'recentSubscriptions'));
    }

    /**
     * عرض نموذج تعديل الباقة
     */
    public function edit(Package $package)
    {
        return view('admin.packages.edit', compact('package'));
    }

    /**
     * تحديث الباقة
     */
    public function update(Request $request, Package $package)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:packages,name,' . $package->id,
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'max_ads' => 'required|integer|min:1',
            'max_images_per_ad' => 'required|integer|min:1|max:20',
            'featured_ads' => 'boolean',
            'video_allowed' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ], [
            'name.required' => 'اسم الباقة مطلوب',
            'name.unique' => 'اسم الباقة موجود مسبقاً',
            'price.required' => 'سعر الباقة مطلوب',
            'price.numeric' => 'سعر الباقة يجب أن يكون رقماً',
            'price.min' => 'سعر الباقة لا يمكن أن يكون أقل من صفر',
            'duration_days.required' => 'مدة الباقة مطلوبة',
            'duration_days.integer' => 'مدة الباقة يجب أن تكون رقماً صحيحاً',
            'duration_days.min' => 'مدة الباقة يجب أن تكون يوم واحد على الأقل',
            'max_ads.required' => 'عدد الإعلانات المسموح مطلوب',
            'max_ads.integer' => 'عدد الإعلانات يجب أن يكون رقماً صحيحاً',
            'max_ads.min' => 'عدد الإعلانات يجب أن يكون واحد على الأقل',
            'max_images_per_ad.required' => 'عدد الصور لكل إعلان مطلوب',
            'max_images_per_ad.integer' => 'عدد الصور يجب أن يكون رقماً صحيحاً',
            'max_images_per_ad.min' => 'عدد الصور يجب أن يكون واحد على الأقل',
            'max_images_per_ad.max' => 'عدد الصور لا يمكن أن يزيد عن 20 صورة',
        ]);

        try {
            DB::beginTransaction();

            $package->update([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'duration_days' => $request->duration_days,
                'max_ads' => $request->max_ads,
                'max_images_per_ad' => $request->max_images_per_ad,
                'featured_ads' => $request->boolean('featured_ads'),
                'video_allowed' => $request->boolean('video_allowed'),
                'is_active' => $request->boolean('is_active'),
                'sort_order' => $request->sort_order ?? $package->sort_order,
            ]);

            DB::commit();

            return redirect()->route('admin.packages.index')
                ->with('success', 'تم تحديث الباقة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث الباقة')
                ->withInput();
        }
    }

    /**
     * حذف الباقة
     */
    public function destroy(Package $package)
    {
        // التحقق من وجود اشتراكات نشطة
        $activeSubscriptions = $package->subscriptions()->where('status', 'active')->count();
        
        if ($activeSubscriptions > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الباقة لوجود اشتراكات نشطة بها'
            ], 400);
        }

        try {
            DB::beginTransaction();

            $package->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الباقة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الباقة'
            ], 500);
        }
    }

    /**
     * تغيير حالة الباقة (تفعيل/إلغاء تفعيل)
     */
    public function toggleStatus(Package $package)
    {
        try {
            $package->update([
                'is_active' => !$package->is_active
            ]);

            $status = $package->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

            return response()->json([
                'success' => true,
                'message' => $status . ' الباقة بنجاح',
                'is_active' => $package->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير حالة الباقة'
            ], 500);
        }
    }
}
