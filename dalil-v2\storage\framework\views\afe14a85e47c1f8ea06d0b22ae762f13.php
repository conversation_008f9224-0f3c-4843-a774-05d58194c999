<?php $__env->startSection('title', 'إضافة صلاحية جديدة'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">الرئيسية</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.permissions.index')); ?>">إدارة الصلاحيات</a></li>
    <li class="breadcrumb-item active">إضافة صلاحية جديدة</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إضافة صلاحية جديدة</h1>
        <div>
            <a href="<?php echo e(route('admin.permissions.index')); ?>" class="btn btn-secondary btn-sm shadow-sm">
                <i class="fas fa-arrow-right fa-sm text-white-50"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الصلاحية</h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.permissions.store')); ?>" method="POST" id="permission-form">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="display_name">اسم الصلاحية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="display_name" name="display_name" 
                                           value="<?php echo e(old('display_name')); ?>" 
                                           placeholder="مثال: عرض المستخدمين" required>
                                    <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">الاسم التقني <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="name" name="name" value="<?php echo e(old('name')); ?>" 
                                           placeholder="مثال: users.view" required>
                                    <small class="form-text text-muted">
                                        يجب أن يكون باللغة الإنجليزية ولا يحتوي على مسافات (مثال: users.view)
                                    </small>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="group">المجموعة <span class="text-danger">*</span></label>
                            <select class="form-control <?php $__errorArgs = ['group'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="group" name="group" required>
                                <option value="">اختر المجموعة</option>
                                <option value="المستخدمين" <?php echo e(old('group') == 'المستخدمين' ? 'selected' : ''); ?>>المستخدمين</option>
                                <option value="الأدوار والصلاحيات" <?php echo e(old('group') == 'الأدوار والصلاحيات' ? 'selected' : ''); ?>>الأدوار والصلاحيات</option>
                                <option value="الإعلانات" <?php echo e(old('group') == 'الإعلانات' ? 'selected' : ''); ?>>الإعلانات</option>
                                <option value="الباقات والاشتراكات" <?php echo e(old('group') == 'الباقات والاشتراكات' ? 'selected' : ''); ?>>الباقات والاشتراكات</option>
                                <option value="البيانات الأساسية" <?php echo e(old('group') == 'البيانات الأساسية' ? 'selected' : ''); ?>>البيانات الأساسية</option>
                                <option value="الإعدادات" <?php echo e(old('group') == 'الإعدادات' ? 'selected' : ''); ?>>الإعدادات</option>
                                <option value="التقارير والإحصائيات" <?php echo e(old('group') == 'التقارير والإحصائيات' ? 'selected' : ''); ?>>التقارير والإحصائيات</option>
                                <option value="عام" <?php echo e(old('group') == 'عام' ? 'selected' : ''); ?>>عام</option>
                                <option value="النظام" <?php echo e(old('group') == 'النظام' ? 'selected' : ''); ?>>النظام</option>
                            </select>
                            <?php $__errorArgs = ['group'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="description">الوصف</label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="description" name="description" rows="3" 
                                      placeholder="وصف مختصر للصلاحية ووظيفتها"><?php echo e(old('description')); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الصلاحية
                            </button>
                            <a href="<?php echo e(route('admin.permissions.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">نصائح مهمة</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> نصائح لإنشاء الصلاحيات:</h6>
                        <ul class="mb-0">
                            <li><strong>الاسم التقني:</strong> يجب أن يكون فريداً ويتبع نمط module.action (مثل: users.view)</li>
                            <li><strong>اسم الصلاحية:</strong> يجب أن يكون واضحاً ومفهوماً للمستخدمين</li>
                            <li><strong>المجموعة:</strong> تساعد في تنظيم الصلاحيات وسهولة إدارتها</li>
                            <li><strong>الوصف:</strong> يساعد في فهم الغرض من الصلاحية</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">أمثلة على الأسماء التقنية</h6>
                </div>
                <div class="card-body">
                    <div class="text-muted">
                        <small>
                            <strong>المستخدمين:</strong><br>
                            • users.view<br>
                            • users.create<br>
                            • users.edit<br>
                            • users.delete<br><br>
                            
                            <strong>الإعلانات:</strong><br>
                            • advertisements.view<br>
                            • advertisements.approve<br>
                            • advertisements.reject<br><br>
                            
                            <strong>الباقات:</strong><br>
                            • packages.view<br>
                            • packages.create<br>
                            • packages.edit<br>
                        </small>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">تحذير</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تنبيه مهم:</h6>
                        <ul class="mb-0">
                            <li>تأكد من عدم تكرار الاسم التقني</li>
                            <li>اختر المجموعة المناسبة للصلاحية</li>
                            <li>الصلاحيات المحذوفة ستؤثر على جميع الأدوار المرتبطة بها</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // إنشاء الاسم التقني تلقائياً من اسم الصلاحية
    $('#display_name').on('input', function() {
        const displayName = $(this).val();
        const group = $('#group').val();
        
        if (displayName && group) {
            let technicalName = '';
            
            // تحديد البادئة حسب المجموعة
            switch(group) {
                case 'المستخدمين':
                    technicalName = 'users.';
                    break;
                case 'الأدوار والصلاحيات':
                    if (displayName.includes('دور')) {
                        technicalName = 'roles.';
                    } else {
                        technicalName = 'permissions.';
                    }
                    break;
                case 'الإعلانات':
                    technicalName = 'advertisements.';
                    break;
                case 'الباقات والاشتراكات':
                    if (displayName.includes('باقة')) {
                        technicalName = 'packages.';
                    } else {
                        technicalName = 'subscriptions.';
                    }
                    break;
                case 'البيانات الأساسية':
                    technicalName = 'basic_data.';
                    break;
                case 'الإعدادات':
                    technicalName = 'settings.';
                    break;
                case 'التقارير والإحصائيات':
                    technicalName = 'reports.';
                    break;
                case 'عام':
                    technicalName = 'general.';
                    break;
                case 'النظام':
                    technicalName = 'system.';
                    break;
                default:
                    technicalName = 'custom.';
            }
            
            // تحديد الإجراء من اسم الصلاحية
            let action = '';
            if (displayName.includes('عرض') || displayName.includes('قائمة')) {
                action = 'view';
            } else if (displayName.includes('إنشاء') || displayName.includes('إضافة')) {
                action = 'create';
            } else if (displayName.includes('تعديل') || displayName.includes('تحديث')) {
                action = 'edit';
            } else if (displayName.includes('حذف')) {
                action = 'delete';
            } else if (displayName.includes('موافقة')) {
                action = 'approve';
            } else if (displayName.includes('رفض')) {
                action = 'reject';
            } else if (displayName.includes('تعليق')) {
                action = 'suspend';
            } else if (displayName.includes('إدارة')) {
                action = 'manage';
            } else {
                // استخدام الكلمة الأولى من اسم الصلاحية
                action = displayName.split(' ')[0]
                    .toLowerCase()
                    .replace(/[أ-ي]/g, '') // إزالة الأحرف العربية
                    .replace(/[^a-z0-9]/g, ''); // إزالة الأحرف الخاصة
                
                if (!action) action = 'custom';
            }
            
            $('#name').val(technicalName + action);
        }
    });

    // تحديث الاسم التقني عند تغيير المجموعة
    $('#group').on('change', function() {
        const displayName = $('#display_name').val();
        if (displayName) {
            $('#display_name').trigger('input');
        }
    });
});
</script>

<style>
.form-group label {
    font-weight: 600;
    color: #5a5c69;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.alert ul {
    padding-left: 1.5rem;
}

.alert li {
    margin-bottom: 0.25rem;
}

.text-muted small {
    line-height: 1.6;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\dalil-v2\resources\views/admin/permissions/create.blade.php ENDPATH**/ ?>