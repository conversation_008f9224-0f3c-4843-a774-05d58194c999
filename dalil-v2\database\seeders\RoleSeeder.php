<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الأدوار الأساسية
        $superAdmin = Role::create([
            'name' => 'super_admin',
            'display_name' => 'مدير عام',
            'description' => 'مدير عام للنظام مع جميع الصلاحيات'
        ]);

        $admin = Role::create([
            'name' => 'admin',
            'display_name' => 'مدير',
            'description' => 'مدير النظام'
        ]);

        $moderator = Role::create([
            'name' => 'moderator',
            'display_name' => 'مشرف',
            'description' => 'مشرف على المحتوى'
        ]);

        // ربط الصلاحيات بالأدوار
        $allPermissions = Permission::all();
        
        // المدير العام يحصل على جميع الصلاحيات
        $superAdmin->permissions()->attach($allPermissions);

        // المدير يحصل على معظم الصلاحيات
        $adminPermissions = Permission::whereIn('group', [
            'المستخدمين', 
            'الإعلانات', 
            'الباقات والاشتراكات', 
            'البيانات الأساسية', 
            'التقارير والإحصائيات', 
            'الإعدادات',
            'عام'
        ])->get();
        $admin->permissions()->attach($adminPermissions);

        // المشرف يحصل على صلاحيات محدودة
        $moderatorPermissions = Permission::whereIn('name', [
            'advertisements.view',
            'advertisements.approve', 
            'advertisements.reject',
            'users.view',
            'general.dashboard'
        ])->get();
        $moderator->permissions()->attach($moderatorPermissions);

        $this->command->info('تم إنشاء الأدوار وربطها بالصلاحيات بنجاح');
        $this->command->info('الأدوار المنشأة:');
        $this->command->info('- مدير عام: ' . $superAdmin->permissions->count() . ' صلاحية');
        $this->command->info('- مدير: ' . $admin->permissions->count() . ' صلاحية');
        $this->command->info('- مشرف: ' . $moderator->permissions->count() . ' صلاحية');
    }
}
