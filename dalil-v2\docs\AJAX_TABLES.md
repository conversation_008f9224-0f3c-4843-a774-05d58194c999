# نظام الجداول التفاعلية مع AJAX

## نظرة عامة

تم تطوير نظام شامل للجداول التفاعلية باستخدام AJAX لتحسين تجربة المستخدم في لوحة التحكم. يوفر هذا النظام:

- **البحث المباشر** مع تأخير لتجنب الطلبات المفرطة
- **الفلترة التفاعلية** حسب معايير متعددة
- **الترتيب القابل للنقر** مع مؤشرات بصرية
- **الترقيم التفاعلي** بدون إعادة تحميل الصفحة
- **مؤشرات التحميل** لتحسين تجربة المستخدم
- **معالجة الأخطاء** مع رسائل واضحة

## الملفات الأساسية

### 1. JavaScript Class
```
public/js/admin/ajax-table.js
```
كلاس قابل لإعادة الاستخدام يحتوي على جميع وظائف الجدول التفاعلي.

### 2. CSS Styles
```
public/css/admin/ajax-table.css
```
تنسيقات شاملة للجداول التفاعلية مع دعم الاستجابة.

### 3. Controller Pattern
```php
// مثال من UserController
public function index(Request $request)
{
    if ($request->ajax()) {
        return $this->getUsersData($request);
    }
    return view('admin.users.index');
}

public function getUsersData(Request $request)
{
    // منطق الفلترة والبحث والترتيب
    // إرجاع JSON response
}
```

## كيفية الاستخدام

### 1. إعداد Controller

```php
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class YourController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->getYourData($request);
        }
        return view('admin.your-module.index');
    }

    public function getYourData(Request $request)
    {
        $query = YourModel::query();

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        // الفلترة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDir = $request->get('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        $data = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $data->items(),
            'pagination' => [
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'per_page' => $data->perPage(),
                'total' => $data->total(),
                'from' => $data->firstItem(),
                'to' => $data->lastItem(),
            ],
            'links' => $data->appends(request()->query())->links()->render()
        ]);
    }
}
```

### 2. إعداد Routes

```php
Route::resource('your-module', YourController::class);
Route::get('your-module-data', [YourController::class, 'getYourData'])->name('your-module.data');
```

### 3. إعداد View

```html
@extends('admin.layouts.app')

@section('content')
    <!-- نموذج الفلاتر -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form id="filters-form">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search" placeholder="البحث...">
                    </div>
                    <div class="col-md-2">
                        <select class="form-control" name="status">
                            <option value="">الكل</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button type="button" id="clear-filters" class="btn btn-secondary">إلغاء</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- الجدول -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                البيانات <span id="data-count" class="badge badge-primary">0</span>
            </h6>
            <div id="loading-indicator" class="d-none">
                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="data-table" class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="name">الاسم <i class="fas fa-sort"></i></th>
                            <th class="sortable" data-sort="created_at">التاريخ <i class="fas fa-sort"></i></th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تحميل البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>
            <div id="pagination-container" class="d-flex justify-content-center mt-3">
                <!-- سيتم تحميل الترقيم عبر AJAX -->
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="{{ asset('js/admin/ajax-table.js') }}"></script>
<script>
$(document).ready(function() {
    const dataTable = new AjaxTable({
        tableId: '#data-table',
        filtersFormId: '#filters-form',
        loadingIndicatorId: '#loading-indicator',
        paginationContainerId: '#pagination-container',
        counterId: '#data-count',
        dataUrl: '{{ route("admin.your-module.data") }}',
        renderRow: renderDataRow
    });

    function renderDataRow(item) {
        return `
            <tr>
                <td>${item.name}</td>
                <td>${new Date(item.created_at).toLocaleDateString('ar-SA')}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                data-bs-toggle="dropdown">إجراءات</button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="/admin/your-module/${item.id}">عرض</a>
                            <a class="dropdown-item" href="/admin/your-module/${item.id}/edit">تعديل</a>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }
});
</script>
@endpush
```

## الخصائص المتقدمة

### 1. البحث مع التأخير (Debouncing)
```javascript
// تأخير 500ms لتجنب الطلبات المفرطة
searchDelay: 500
```

### 2. الترتيب التفاعلي
```javascript
// النقر على العمود يغير الترتيب
$('.sortable').on('click', function() {
    // منطق تغيير الترتيب
});
```

### 3. الترقيم التفاعلي
```javascript
// النقر على أرقام الصفحات
$(document).on('click', '.pagination a', function(e) {
    e.preventDefault();
    // تحميل الصفحة الجديدة
});
```

### 4. معالجة الأخطاء
```javascript
error: function(xhr) {
    console.error('خطأ في تحميل البيانات:', xhr);
    this.renderError();
}
```

## التخصيص

### 1. تخصيص الإعدادات
```javascript
const customTable = new AjaxTable({
    // إعدادات مخصصة
    perPage: 25,
    sortBy: 'name',
    sortDir: 'asc',
    searchDelay: 300,
    // callback مخصص
    onDataLoaded: function(response) {
        console.log('تم تحميل البيانات:', response);
    }
});
```

### 2. تخصيص عرض البيانات
```javascript
function renderCustomRow(item) {
    return `
        <tr class="custom-row">
            <td class="custom-cell">${item.custom_field}</td>
            <!-- محتوى مخصص -->
        </tr>
    `;
}
```

## الأمان

- **CSRF Protection**: جميع الطلبات تتضمن CSRF token
- **XSS Prevention**: تنظيف البيانات قبل العرض
- **SQL Injection**: استخدام Eloquent ORM للحماية

## الأداء

- **Lazy Loading**: تحميل البيانات عند الحاجة فقط
- **Pagination**: تقسيم البيانات لتحسين الأداء
- **Debouncing**: تقليل عدد الطلبات للخادم
- **Caching**: إمكانية إضافة cache للاستعلامات

## المتطلبات

- jQuery 3.6+
- Bootstrap 5.3+
- Laravel 11+
- Font Awesome 6.4+

## الدعم والصيانة

لإضافة ميزات جديدة أو إصلاح مشاكل:

1. تحديث `ajax-table.js` للوظائف الجديدة
2. تحديث `ajax-table.css` للتنسيقات
3. اتباع نفس النمط في Controllers جديدة
4. اختبار الوظائف في متصفحات مختلفة

## أمثلة موجودة

- **إدارة المستخدمين**: `resources/views/admin/users/index.blade.php`
- **إدارة الإعلانات**: `resources/views/admin/advertisements/index.blade.php`
