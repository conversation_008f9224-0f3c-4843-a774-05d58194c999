<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class District extends Model
{
    use HasFactory;

    protected $fillable = [
        'governorate_id',
        'name',
        'name_en',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // العلاقات
    public function governorate()
    {
        return $this->belongsTo(Governorate::class);
    }

    public function advertisements()
    {
        return $this->hasMany(Advertisement::class);
    }

    // دوال مساعدة
    public function isActive()
    {
        return $this->is_active;
    }

    public static function getActive()
    {
        return static::where('is_active', true)->orderBy('sort_order')->get();
    }
}
