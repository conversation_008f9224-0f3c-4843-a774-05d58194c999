<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Package extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'duration_days',
        'max_ads',
        'max_images_per_ad',
        'featured_ads',
        'video_allowed',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'featured_ads' => 'boolean',
        'video_allowed' => 'boolean',
        'is_active' => 'boolean',
    ];

    // العلاقات
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    // دوال مساعدة
    public function isActive()
    {
        return $this->is_active;
    }

    public static function getActive()
    {
        return static::where('is_active', true)->orderBy('sort_order')->get();
    }

    public function getFormattedPrice()
    {
        return number_format($this->price, 2) . ' ريال';
    }
}
