<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Advertisement;
use App\Models\Package;
use App\Models\Subscription;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function dashboard()
    {
        // إحصائيات عامة
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'total_ads' => Advertisement::count(),
            'pending_ads' => Advertisement::where('status', 'pending')->count(),
            'active_ads' => Advertisement::where('status', 'active')->count(),
            'total_subscriptions' => Subscription::count(),
            'active_subscriptions' => Subscription::where('status', 'active')->count(),
            'total_revenue' => Subscription::where('status', 'active')->sum('amount'),
        ];

        // إحصائيات المستخدمين حسب النوع
        $usersByType = User::select('user_type', DB::raw('count(*) as count'))
            ->groupBy('user_type')
            ->pluck('count', 'user_type')
            ->toArray();

        // إحصائيات الإعلانات حسب الحالة
        $adsByStatus = Advertisement::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // أحدث المستخدمين
        $recentUsers = User::with('roles')
            ->latest()
            ->limit(5)
            ->get();

        // الإعلانات المعلقة
        $pendingAds = Advertisement::with(['user', 'manufacturer', 'carModel'])
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();

        // أكثر الباقات مبيعاً
        $popularPackages = Package::withCount('subscriptions')
            ->orderBy('subscriptions_count', 'desc')
            ->limit(5)
            ->get();

        // إحصائيات شهرية للاشتراكات (متوافق مع SQLite)
        $monthlySubscriptions = Subscription::select(
                DB::raw("strftime('%m', created_at) as month"),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as revenue')
            )
            ->whereRaw("strftime('%Y', created_at) = ?", [date('Y')])
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'usersByType',
            'adsByStatus',
            'recentUsers',
            'pendingAds',
            'popularPackages',
            'monthlySubscriptions'
        ));
    }
}
