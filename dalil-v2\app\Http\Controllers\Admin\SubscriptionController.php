<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\Package;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of subscriptions.
     */
    public function index()
    {
        return view('admin.subscriptions.index');
    }

    /**
     * Get subscriptions data for AJAX table.
     */
    public function getSubscriptionsData(Request $request)
    {
        try {
            $query = Subscription::with(['user', 'package'])
                ->select('subscriptions.*');

            // البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->whereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%")
                                 ->orWhere('email', 'like', "%{$search}%");
                    })->orWhereHas('package', function ($packageQuery) use ($search) {
                        $packageQuery->where('name', 'like', "%{$search}%");
                    });
                });
            }

            // فلترة حسب الحالة
            if ($request->filled('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }

            // فلترة حسب الباقة
            if ($request->filled('package_id') && $request->package_id !== 'all') {
                $query->where('package_id', $request->package_id);
            }

            // فلترة حسب تاريخ الانتهاء
            if ($request->filled('expiry_filter')) {
                $now = Carbon::now();
                switch ($request->expiry_filter) {
                    case 'expired':
                        $query->where('end_date', '<', $now);
                        break;
                    case 'expiring_soon':
                        $query->where('end_date', '>', $now)
                              ->where('end_date', '<=', $now->copy()->addDays(7));
                        break;
                    case 'active':
                        $query->where('end_date', '>', $now);
                        break;
                }
            }

            // فلترة حسب نطاق المبلغ
            if ($request->filled('amount_min')) {
                $query->where('amount_paid', '>=', $request->amount_min);
            }
            if ($request->filled('amount_max')) {
                $query->where('amount_paid', '<=', $request->amount_max);
            }

            // الترتيب
            $sortColumn = $request->get('sort', 'created_at');
            $sortDirection = $request->get('direction', 'desc');
            
            $allowedSorts = ['created_at', 'start_date', 'end_date', 'amount_paid', 'status'];
            if (in_array($sortColumn, $allowedSorts)) {
                $query->orderBy($sortColumn, $sortDirection);
            }

            // التصفح
            $perPage = $request->get('per_page', 15);
            $subscriptions = $query->paginate($perPage);

            // تحضير البيانات للعرض
            $data = $subscriptions->map(function ($subscription) {
                return [
                    'id' => $subscription->id,
                    'user' => [
                        'id' => $subscription->user->id,
                        'name' => $subscription->user->name,
                        'email' => $subscription->user->email,
                        'avatar' => $subscription->user->avatar ?? null,
                    ],
                    'package' => [
                        'id' => $subscription->package->id,
                        'name' => $subscription->package->name,
                        'price' => $subscription->package->price,
                    ],
                    'start_date' => $subscription->start_date->format('Y-m-d'),
                    'end_date' => $subscription->end_date->format('Y-m-d'),
                    'amount_paid' => number_format($subscription->amount_paid, 2),
                    'status' => $subscription->status,
                    'status_label' => $this->getStatusLabel($subscription->status),
                    'status_class' => $this->getStatusClass($subscription->status),
                    'days_remaining' => $subscription->getDaysRemaining(),
                    'is_expired' => $subscription->isExpired(),
                    'is_expiring_soon' => $subscription->isExpiringSoon(),
                    'created_at' => $subscription->created_at->format('Y-m-d H:i'),
                    'actions' => $this->getSubscriptionActions($subscription),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data,
                'pagination' => [
                    'current_page' => $subscriptions->currentPage(),
                    'last_page' => $subscriptions->lastPage(),
                    'per_page' => $subscriptions->perPage(),
                    'total' => $subscriptions->total(),
                    'from' => $subscriptions->firstItem(),
                    'to' => $subscriptions->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في تحميل البيانات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for creating a new subscription.
     */
    public function create()
    {
        $packages = Package::where('is_active', true)->orderBy('sort_order')->get();
        $users = User::where('status', 'active')->orderBy('name')->get();

        return view('admin.subscriptions.create', compact('packages', 'users'));
    }

    /**
     * Store a newly created subscription.
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'package_id' => 'required|exists:packages,id',
            'start_date' => 'required|date|after_or_equal:today',
            'amount_paid' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:50',
            'notes' => 'nullable|string|max:500',
        ], [
            'user_id.required' => 'يجب اختيار المستخدم',
            'user_id.exists' => 'المستخدم المحدد غير موجود',
            'package_id.required' => 'يجب اختيار الباقة',
            'package_id.exists' => 'الباقة المحددة غير موجودة',
            'start_date.required' => 'يجب تحديد تاريخ البداية',
            'start_date.date' => 'تاريخ البداية غير صحيح',
            'start_date.after_or_equal' => 'تاريخ البداية يجب أن يكون اليوم أو بعده',
            'amount_paid.required' => 'يجب تحديد المبلغ المدفوع',
            'amount_paid.numeric' => 'المبلغ المدفوع يجب أن يكون رقماً',
            'amount_paid.min' => 'المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر',
            'payment_method.max' => 'طريقة الدفع يجب ألا تتجاوز 50 حرف',
            'notes.max' => 'الملاحظات يجب ألا تتجاوز 500 حرف',
        ]);

        try {
            DB::beginTransaction();

            $package = Package::findOrFail($request->package_id);
            $startDate = Carbon::parse($request->start_date);
            $endDate = $startDate->copy()->addDays($package->duration_days);

            // التحقق من عدم وجود اشتراك نشط للمستخدم
            $activeSubscription = Subscription::where('user_id', $request->user_id)
                ->where('status', 'active')
                ->where('end_date', '>', Carbon::now())
                ->first();

            if ($activeSubscription) {
                return back()->withErrors(['user_id' => 'المستخدم لديه اشتراك نشط بالفعل'])->withInput();
            }

            $subscription = Subscription::create([
                'user_id' => $request->user_id,
                'package_id' => $request->package_id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'amount_paid' => $request->amount_paid,
                'status' => 'active',
                'payment_method' => $request->payment_method,
            ]);

            DB::commit();

            return redirect()->route('admin.subscriptions.show', $subscription)
                ->with('success', 'تم إنشاء الاشتراك بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'حدث خطأ في إنشاء الاشتراك: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified subscription.
     */
    public function show(Subscription $subscription)
    {
        $subscription->load(['user', 'package']);
        return view('admin.subscriptions.show', compact('subscription'));
    }

    /**
     * Show the form for editing the specified subscription.
     */
    public function edit(Subscription $subscription)
    {
        $packages = Package::where('is_active', true)->orderBy('sort_order')->get();
        $users = User::where('is_active', true)->orderBy('name')->get();
        
        return view('admin.subscriptions.edit', compact('subscription', 'packages', 'users'));
    }

    /**
     * Update the specified subscription.
     */
    public function update(Request $request, Subscription $subscription)
    {
        $request->validate([
            'end_date' => 'required|date|after:start_date',
            'amount_paid' => 'required|numeric|min:0',
            'status' => 'required|in:active,expired,cancelled',
            'payment_method' => 'nullable|string|max:50',
        ], [
            'end_date.required' => 'يجب تحديد تاريخ الانتهاء',
            'end_date.date' => 'تاريخ الانتهاء غير صحيح',
            'end_date.after' => 'تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية',
            'amount_paid.required' => 'يجب تحديد المبلغ المدفوع',
            'amount_paid.numeric' => 'المبلغ المدفوع يجب أن يكون رقماً',
            'amount_paid.min' => 'المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر',
            'status.required' => 'يجب تحديد حالة الاشتراك',
            'status.in' => 'حالة الاشتراك غير صحيحة',
            'payment_method.max' => 'طريقة الدفع يجب ألا تتجاوز 50 حرف',
        ]);

        try {
            DB::beginTransaction();

            $subscription->update([
                'end_date' => $request->end_date,
                'amount_paid' => $request->amount_paid,
                'status' => $request->status,
                'payment_method' => $request->payment_method,
            ]);

            DB::commit();

            return redirect()->route('admin.subscriptions.show', $subscription)
                ->with('success', 'تم تحديث الاشتراك بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'حدث خطأ في تحديث الاشتراك: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified subscription.
     */
    public function destroy(Subscription $subscription)
    {
        try {
            $subscription->delete();
            return response()->json(['success' => true, 'message' => 'تم حذف الاشتراك بنجاح']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'حدث خطأ في حذف الاشتراك'], 500);
        }
    }

    /**
     * Renew subscription.
     */
    public function renew(Request $request, Subscription $subscription)
    {
        $request->validate([
            'duration_days' => 'required|integer|min:1|max:365',
            'amount_paid' => 'required|numeric|min:0',
        ], [
            'duration_days.required' => 'يجب تحديد مدة التجديد',
            'duration_days.integer' => 'مدة التجديد يجب أن تكون رقماً صحيحاً',
            'duration_days.min' => 'مدة التجديد يجب أن تكون يوم واحد على الأقل',
            'duration_days.max' => 'مدة التجديد يجب ألا تتجاوز 365 يوم',
            'amount_paid.required' => 'يجب تحديد مبلغ التجديد',
            'amount_paid.numeric' => 'مبلغ التجديد يجب أن يكون رقماً',
            'amount_paid.min' => 'مبلغ التجديد يجب أن يكون أكبر من أو يساوي صفر',
        ]);

        try {
            DB::beginTransaction();

            $newEndDate = Carbon::parse($subscription->end_date)->addDays($request->duration_days);
            
            $subscription->update([
                'end_date' => $newEndDate,
                'status' => 'active',
                'amount' => $subscription->amount + $request->amount,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تجديد الاشتراك بنجاح',
                'new_end_date' => $newEndDate->format('Y-m-d')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'حدث خطأ في تجديد الاشتراك'], 500);
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancel(Subscription $subscription)
    {
        try {
            $subscription->update(['status' => 'cancelled']);
            return response()->json(['success' => true, 'message' => 'تم إلغاء الاشتراك بنجاح']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'حدث خطأ في إلغاء الاشتراك'], 500);
        }
    }

    /**
     * Get status label.
     */
    private function getStatusLabel($status)
    {
        $labels = [
            'active' => 'نشط',
            'expired' => 'منتهي',
            'cancelled' => 'ملغي',
        ];

        return $labels[$status] ?? $status;
    }

    /**
     * Get status CSS class.
     */
    private function getStatusClass($status)
    {
        $classes = [
            'active' => 'success',
            'expired' => 'warning',
            'cancelled' => 'danger',
        ];

        return $classes[$status] ?? 'secondary';
    }

    /**
     * Get subscription actions.
     */
    private function getSubscriptionActions($subscription)
    {
        $actions = [];

        $actions[] = [
            'type' => 'view',
            'url' => route('admin.subscriptions.show', $subscription->id),
            'label' => 'عرض',
            'class' => 'btn-outline-info'
        ];

        $actions[] = [
            'type' => 'edit',
            'url' => route('admin.subscriptions.edit', $subscription->id),
            'label' => 'تعديل',
            'class' => 'btn-outline-primary'
        ];

        if ($subscription->status === 'active' && !$subscription->isExpired()) {
            $actions[] = [
                'type' => 'cancel',
                'url' => route('admin.subscriptions.cancel', $subscription->id),
                'label' => 'إلغاء',
                'class' => 'btn-outline-warning'
            ];
        }

        if ($subscription->status === 'expired' || $subscription->isExpired()) {
            $actions[] = [
                'type' => 'renew',
                'url' => route('admin.subscriptions.renew', $subscription->id),
                'label' => 'تجديد',
                'class' => 'btn-outline-success'
            ];
        }

        $actions[] = [
            'type' => 'delete',
            'url' => route('admin.subscriptions.destroy', $subscription->id),
            'label' => 'حذف',
            'class' => 'btn-outline-danger'
        ];

        return $actions;
    }
}
