<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Manufacturer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_en',
        'logo',
        'description',
        'country',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // العلاقات
    public function models()
    {
        return $this->hasMany(CarModel::class);
    }

    public function advertisements()
    {
        return $this->hasMany(Advertisement::class);
    }

    // دوال مساعدة
    public function isActive()
    {
        return $this->is_active;
    }

    public static function getActive()
    {
        return static::where('is_active', true)->orderBy('sort_order')->get();
    }

    public function getActiveModels()
    {
        return $this->models()->where('is_active', true)->orderBy('sort_order')->get();
    }
}
