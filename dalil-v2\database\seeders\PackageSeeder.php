<?php

namespace Database\Seeders;

use App\Models\Package;
use Illuminate\Database\Seeder;

class PackageSeeder extends Seeder
{
    public function run(): void
    {
        // باقة مجانية للمستخدمين الجدد
        Package::create([
            'name' => 'باقة مجانية',
            'description' => 'باقة مجانية للمستخدمين الجدد تتيح نشر إعلان واحد لمدة 7 أيام',
            'price' => 0,
            'duration_days' => 7,
            'max_ads' => 1,
            'max_images_per_ad' => 3,
            'featured_ads' => false,
            'video_allowed' => false,
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // باقة أساسية للأفراد
        Package::create([
            'name' => 'باقة أساسية',
            'description' => 'باقة مناسبة للأفراد الذين يريدون بيع سياراتهم الشخصية',
            'price' => 1000, // 1000 ريال يمني
            'duration_days' => 30,
            'max_ads' => 3,
            'max_images_per_ad' => 8,
            'featured_ads' => false,
            'video_allowed' => true,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // باقة متقدمة للأفراد النشطين
        Package::create([
            'name' => 'باقة متقدمة',
            'description' => 'باقة للأفراد النشطين في بيع السيارات مع إمكانيات إضافية',
            'price' => 2500,
            'duration_days' => 30,
            'max_ads' => 8,
            'max_images_per_ad' => 12,
            'featured_ads' => true,
            'video_allowed' => true,
            'is_active' => true,
            'sort_order' => 3,
        ]);

        // باقة المعارض الصغيرة
        Package::create([
            'name' => 'باقة معرض صغير',
            'description' => 'باقة مخصصة للمعارض الصغيرة والمتوسطة',
            'price' => 5000,
            'duration_days' => 30,
            'max_ads' => 20,
            'max_images_per_ad' => 15,
            'featured_ads' => true,
            'video_allowed' => true,
            'is_active' => true,
            'sort_order' => 4,
        ]);

        // باقة المعارض الكبيرة
        Package::create([
            'name' => 'باقة معرض كبير',
            'description' => 'باقة شاملة للمعارض الكبيرة مع جميع المميزات',
            'price' => 10000,
            'duration_days' => 30,
            'max_ads' => 50,
            'max_images_per_ad' => 20,
            'featured_ads' => true,
            'video_allowed' => true,
            'is_active' => true,
            'sort_order' => 5,
        ]);

        // باقة المعارض المميزة (VIP)
        Package::create([
            'name' => 'باقة VIP',
            'description' => 'باقة حصرية للمعارض المميزة مع إعلانات غير محدودة',
            'price' => 20000,
            'duration_days' => 30,
            'max_ads' => 100,
            'max_images_per_ad' => 25,
            'featured_ads' => true,
            'video_allowed' => true,
            'is_active' => true,
            'sort_order' => 6,
        ]);

        // باقة سنوية للمعارض (خصم خاص)
        Package::create([
            'name' => 'باقة سنوية',
            'description' => 'باقة سنوية للمعارض مع خصم 20% - توفر شهرين مجاناً',
            'price' => 96000, // 10 أشهر بدلاً من 12
            'duration_days' => 365,
            'max_ads' => 50,
            'max_images_per_ad' => 20,
            'featured_ads' => true,
            'video_allowed' => true,
            'is_active' => true,
            'sort_order' => 7,
        ]);

        $this->command->info('تم إنشاء باقات الاشتراك بنجاح');
        $this->command->info('تم إنشاء 7 باقات مختلفة تناسب جميع أنواع المستخدمين');
    }
}
