@extends('admin.layouts.app')

@section('title', 'إدارة المستخدمين')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة المستخدمين</h1>
        <a href="{{ route('admin.users.create') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> إضافة مستخدم جديد
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
        </div>
        <div class="card-body">
            <form id="filters-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="الاسم، البريد، الهاتف...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="user_type">نوع المستخدم</label>
                            <select class="form-control" id="user_type" name="user_type">
                                <option value="">الكل</option>
                                <option value="admin">مدير</option>
                                <option value="showroom">معرض</option>
                                <option value="individual">فردي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">معلق</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="role">الدور</label>
                            <select class="form-control" id="role" name="role">
                                <option value="">الكل</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->name }}">{{ $role->display_name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="per_page">عدد النتائج</label>
                            <select class="form-control" id="per_page" name="per_page">
                                <option value="15">15</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" id="clear-filters" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                قائمة المستخدمين
                <span id="users-count" class="badge badge-primary">0</span>
            </h6>
            <div id="loading-indicator" class="d-none">
                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="users-table" class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="name">
                                الاسم <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="email">
                                البريد الإلكتروني <i class="fas fa-sort"></i>
                            </th>
                            <th>الهاتف</th>
                            <th class="sortable" data-sort="user_type">
                                النوع <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="status">
                                الحالة <i class="fas fa-sort"></i>
                            </th>
                            <th>الأدوار</th>
                            <th class="sortable" data-sort="created_at">
                                تاريخ التسجيل <i class="fas fa-sort"></i>
                            </th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- سيتم تحميل البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="d-flex justify-content-center mt-3">
                <!-- سيتم تحميل الترقيم عبر AJAX -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/admin/ajax-table.js') }}"></script>
<script>
$(document).ready(function() {
    // إنشاء جدول المستخدمين التفاعلي
    const usersTable = new AjaxTable({
        tableId: '#users-table',
        filtersFormId: '#filters-form',
        loadingIndicatorId: '#loading-indicator',
        paginationContainerId: '#pagination-container',
        counterId: '#users-count',
        dataUrl: '{{ route("admin.users.data") }}',
        renderRow: renderUserRow
    });

    // دالة عرض صف المستخدم
    function renderUserRow(user) {
        const userTypeLabels = {
            'admin': '<span class="badge badge-danger">مدير</span>',
            'showroom': '<span class="badge badge-info">معرض</span>',
            'individual': '<span class="badge badge-success">فردي</span>'
        };

        const statusLabels = {
            'active': '<span class="badge badge-success">نشط</span>',
            'inactive': '<span class="badge badge-secondary">غير نشط</span>',
            'suspended': '<span class="badge badge-warning">معلق</span>'
        };

        let rolesHtml = '';
        if (user.roles && user.roles.length > 0) {
            user.roles.forEach(function(role) {
                rolesHtml += `<span class="badge badge-primary badge-sm">${role.display_name}</span> `;
            });
        }

        const createdAt = new Date(user.created_at).toLocaleDateString('ar-SA');

        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="mr-3">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div>
                            <div class="font-weight-bold">${user.name}</div>
                            ${user.company_name ? `<div class="text-xs text-gray-600">${user.company_name}</div>` : ''}
                        </div>
                    </div>
                </td>
                <td>${user.email}</td>
                <td>${user.phone || '-'}</td>
                <td>${userTypeLabels[user.user_type] || user.user_type}</td>
                <td>${statusLabels[user.status] || user.status}</td>
                <td>${rolesHtml}</td>
                <td>${createdAt}</td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown">
                            إجراءات
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="/admin/users/${user.id}">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <a class="dropdown-item" href="/admin/users/${user.id}/edit">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <div class="dropdown-divider"></div>
                            ${user.status !== 'suspended' ? `
                                <form method="POST" action="/admin/users/${user.id}/suspend" class="d-inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="dropdown-item text-warning"
                                            onclick="return confirm('هل أنت متأكد من تعليق هذا المستخدم؟')">
                                        <i class="fas fa-ban"></i> تعليق
                                    </button>
                                </form>
                            ` : ''}
                            <form method="POST" action="/admin/users/${user.id}/toggle-status" class="d-inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="dropdown-item ${user.status === 'active' ? 'text-warning' : 'text-success'}">
                                    <i class="fas fa-${user.status === 'active' ? 'pause' : 'play'}"></i>
                                    ${user.status === 'active' ? 'إلغاء التفعيل' : 'تفعيل'}
                                </button>
                            </form>
                            <div class="dropdown-divider"></div>
                            ${user.id !== {{ auth()->id() }} ? `
                                <form method="POST" action="/admin/users/${user.id}" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="dropdown-item text-danger"
                                            onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </form>
                            ` : ''}
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }
});
</script>

<style>
.sortable {
    cursor: pointer;
    user-select: none;
}

.sortable:hover {
    background-color: #f8f9fc;
}

.icon-circle {
    height: 2rem;
    width: 2rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

#loading-indicator {
    color: #5a5c69;
}
</style>
@endpush

@push('styles')
<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush
