<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RoleController extends Controller
{
    /**
     * عرض قائمة الأدوار
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->getRolesData($request);
        }

        return view('admin.roles.index');
    }

    /**
     * جلب بيانات الأدوار عبر AJAX
     */
    public function getRolesData(Request $request)
    {
        $query = Role::withCount(['users', 'permissions']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->filled('type')) {
            $query->where('name', 'like', $request->type . '%');
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDir = $request->get('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        $roles = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $roles->items(),
            'pagination' => [
                'current_page' => $roles->currentPage(),
                'last_page' => $roles->lastPage(),
                'per_page' => $roles->perPage(),
                'total' => $roles->total(),
                'from' => $roles->firstItem(),
                'to' => $roles->lastItem(),
            ],
            'links' => $roles->appends(request()->query())->links()->render()
        ]);
    }

    /**
     * عرض نموذج إنشاء دور جديد
     */
    public function create()
    {
        $permissions = Permission::all()->groupBy('group');
        return view('admin.roles.create', compact('permissions'));
    }

    /**
     * حفظ دور جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ], [
            'name.required' => 'اسم الدور مطلوب',
            'name.unique' => 'اسم الدور موجود مسبقاً',
            'display_name.required' => 'الاسم المعروض مطلوب',
            'permissions.*.exists' => 'إحدى الصلاحيات المحددة غير صحيحة'
        ]);

        try {
            DB::beginTransaction();

            $role = Role::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
            ]);

            // ربط الصلاحيات بالدور
            if ($request->has('permissions')) {
                $role->permissions()->sync($request->permissions);
            }

            DB::commit();

            return redirect()->route('admin.roles.index')
                ->with('success', 'تم إنشاء الدور بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء الدور')
                ->withInput();
        }
    }

    /**
     * عرض تفاصيل الدور
     */
    public function show(Role $role)
    {
        $role->load(['permissions', 'users']);
        return view('admin.roles.show', compact('role'));
    }

    /**
     * عرض نموذج تعديل الدور
     */
    public function edit(Role $role)
    {
        $permissions = Permission::all()->groupBy('group');
        $rolePermissions = $role->permissions->pluck('id')->toArray();
        
        return view('admin.roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * تحديث الدور
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ], [
            'name.required' => 'اسم الدور مطلوب',
            'name.unique' => 'اسم الدور موجود مسبقاً',
            'display_name.required' => 'الاسم المعروض مطلوب',
            'permissions.*.exists' => 'إحدى الصلاحيات المحددة غير صحيحة'
        ]);

        try {
            DB::beginTransaction();

            $role->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
            ]);

            // تحديث الصلاحيات
            $role->permissions()->sync($request->permissions ?? []);

            DB::commit();

            return redirect()->route('admin.roles.index')
                ->with('success', 'تم تحديث الدور بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث الدور')
                ->withInput();
        }
    }

    /**
     * حذف الدور
     */
    public function destroy(Role $role)
    {
        try {
            // التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
            if ($role->users()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف هذا الدور لأنه مرتبط بمستخدمين'
                ], 400);
            }

            DB::beginTransaction();

            // حذف ربط الصلاحيات
            $role->permissions()->detach();
            
            // حذف الدور
            $role->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الدور بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الدور'
            ], 500);
        }
    }

    /**
     * تحديث صلاحيات الدور عبر AJAX
     */
    public function updatePermissions(Request $request, Role $role)
    {
        $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ]);

        try {
            $role->permissions()->sync($request->permissions ?? []);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث صلاحيات الدور بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الصلاحيات'
            ], 500);
        }
    }
}
