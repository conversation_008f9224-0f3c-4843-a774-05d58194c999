@extends('admin.layouts.app')

@section('title', 'تعديل الصلاحية: ' . $permission->display_name)

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.permissions.index') }}">إدارة الصلاحيات</a></li>
    <li class="breadcrumb-item active">تعديل الصلاحية</li>
@endsection

@section('content')
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل الصلاحية: {{ $permission->display_name }}</h1>
        <div>
            <a href="{{ route('admin.permissions.show', $permission) }}" class="btn btn-info btn-sm shadow-sm">
                <i class="fas fa-eye fa-sm text-white-50"></i> عرض التفاصيل
            </a>
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary btn-sm shadow-sm">
                <i class="fas fa-arrow-right fa-sm text-white-50"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تعديل معلومات الصلاحية</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.permissions.update', $permission) }}" method="POST" id="permission-form">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="display_name">اسم الصلاحية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                           id="display_name" name="display_name" 
                                           value="{{ old('display_name', $permission->display_name) }}" 
                                           placeholder="مثال: عرض المستخدمين" required>
                                    @error('display_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">الاسم التقني <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $permission->name) }}" 
                                           placeholder="مثال: users.view" required>
                                    <small class="form-text text-muted">
                                        يجب أن يكون باللغة الإنجليزية ولا يحتوي على مسافات (مثال: users.view)
                                    </small>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="group">المجموعة <span class="text-danger">*</span></label>
                            <select class="form-control @error('group') is-invalid @enderror" 
                                    id="group" name="group" required>
                                <option value="">اختر المجموعة</option>
                                <option value="المستخدمين" {{ old('group', $permission->group) == 'المستخدمين' ? 'selected' : '' }}>المستخدمين</option>
                                <option value="الأدوار والصلاحيات" {{ old('group', $permission->group) == 'الأدوار والصلاحيات' ? 'selected' : '' }}>الأدوار والصلاحيات</option>
                                <option value="الإعلانات" {{ old('group', $permission->group) == 'الإعلانات' ? 'selected' : '' }}>الإعلانات</option>
                                <option value="الباقات والاشتراكات" {{ old('group', $permission->group) == 'الباقات والاشتراكات' ? 'selected' : '' }}>الباقات والاشتراكات</option>
                                <option value="البيانات الأساسية" {{ old('group', $permission->group) == 'البيانات الأساسية' ? 'selected' : '' }}>البيانات الأساسية</option>
                                <option value="الإعدادات" {{ old('group', $permission->group) == 'الإعدادات' ? 'selected' : '' }}>الإعدادات</option>
                                <option value="التقارير والإحصائيات" {{ old('group', $permission->group) == 'التقارير والإحصائيات' ? 'selected' : '' }}>التقارير والإحصائيات</option>
                                <option value="عام" {{ old('group', $permission->group) == 'عام' ? 'selected' : '' }}>عام</option>
                                <option value="النظام" {{ old('group', $permission->group) == 'النظام' ? 'selected' : '' }}>النظام</option>
                            </select>
                            @error('group')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">الوصف</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="وصف مختصر للصلاحية ووظيفتها">{{ old('description', $permission->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ route('admin.permissions.show', $permission) }}" class="btn btn-info">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </a>
                            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">معلومات الصلاحية</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12">
                            <h4 class="text-primary">{{ $permission->roles_count ?? $permission->roles()->count() }}</h4>
                            <p class="text-muted mb-0">دور مرتبط</p>
                        </div>
                    </div>
                    <hr>
                    <div class="text-muted">
                        <small>
                            <strong>تاريخ الإنشاء:</strong><br>
                            {{ $permission->created_at->format('Y-m-d H:i') }}
                        </small>
                        <br><br>
                        <small>
                            <strong>آخر تحديث:</strong><br>
                            {{ $permission->updated_at->format('Y-m-d H:i') }}
                        </small>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">الأدوار المرتبطة</h6>
                </div>
                <div class="card-body">
                    @if($permission->roles && $permission->roles->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($permission->roles as $role)
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <strong>{{ $role->display_name }}</strong>
                                        <br><small class="text-muted">{{ $role->name }}</small>
                                    </div>
                                    <a href="{{ route('admin.roles.show', $role) }}" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا توجد أدوار مرتبطة بهذه الصلاحية حالياً.
                        </div>
                    @endif
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">تحذير</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تنبيه مهم:</h6>
                        <ul class="mb-0">
                            <li>تعديل الصلاحية سيؤثر على جميع الأدوار المرتبطة بها</li>
                            <li>تأكد من صحة البيانات قبل الحفظ</li>
                            <li>لا يمكن حذف الصلاحية إذا كانت مرتبطة بأدوار</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">نصائح التعديل</h6>
                </div>
                <div class="card-body">
                    <div class="text-muted">
                        <small>
                            <strong>الاسم التقني:</strong> يجب أن يكون فريداً<br>
                            <strong>المجموعة:</strong> تساعد في تنظيم الصلاحيات<br>
                            <strong>الوصف:</strong> يوضح الغرض من الصلاحية<br>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث الاسم التقني عند تغيير اسم الصلاحية (اختياري)
    $('#display_name').on('input', function() {
        // يمكن تفعيل هذا إذا أردنا تحديث الاسم التقني تلقائياً
        // لكن عادة في التعديل نتركه كما هو
    });
});
</script>

<style>
.form-group label {
    font-weight: 600;
    color: #5a5c69;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.alert ul {
    padding-left: 1.5rem;
}

.alert li {
    margin-bottom: 0.25rem;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>
@endpush
